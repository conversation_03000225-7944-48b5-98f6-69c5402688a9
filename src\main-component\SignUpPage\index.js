import React, { useState } from "react";
import Grid from "@mui/material/Grid";
import SimpleReactValidator from "simple-react-validator";
import { toast } from "react-toastify";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import { Link, useNavigate } from "react-router-dom";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import axios from "axios";
import CircularProgress from "@mui/material/CircularProgress";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import Container from "@mui/material/Container";
import Divider from "@mui/material/Divider";

import "./style.scss";

const SignUpPage = () => {
  const push = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [value, setValue] = useState({
    email: "",
    username: "", // Adding username field
    firstName: "",
    lastName: "",
    phoneNumber: "",
    password: "",
    confirm_password: "",
    dateNaissance: "",
    niveauId: "", // This will store the selected education level ID
    abonnementTypeId: "", // This will store the selected subscription type ID
  });

  const [niveaux, setNiveaux] = useState([]);
  const [abonnements, setAbonnements] = useState([]); // Fetch education levels and subscription types on component mount
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch education levels with error handling
        try {
          const niveauxResponse = await axios.get(
            "http://localhost:8084/api/niveaux/all"
          );
          setNiveaux(niveauxResponse.data);
        } catch (niveauxError) {
          console.error("Error fetching education levels:", niveauxError);
          // Set some default education levels if the API fails
          setNiveaux([
            { id: 1, nomNiveau: "Primary School" },
            { id: 2, nomNiveau: "Middle School" },
            { id: 3, nomNiveau: "High School" },
            { id: 4, nomNiveau: "University" },
          ]);
        }

        // Fetch subscription types with error handling
        try {
          const abonnementsResponse = await axios.get(
            "http://localhost:8084/api/abonnements/all"
          );
          setAbonnements(abonnementsResponse.data);
        } catch (abonnementsError) {
          console.error("Error fetching subscription types:", abonnementsError);
          // Set some default subscription types if the API fails
          setAbonnements([
            { id: 1, nom: "Basic", prix: 50, duree: 30 },
            { id: 2, nom: "Standard", prix: 120, duree: 90 },
            { id: 3, nom: "Premium", prix: 200, duree: 180 },
          ]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Using default values due to server connection issues.");
      }
    };

    fetchData();
  }, []);

  const changeHandler = (e) => {
    setValue({ ...value, [e.target.name]: e.target.value });
    validator.showMessages();
  };

  const [validator] = React.useState(
    new SimpleReactValidator({
      className: "errorMessage",
    })
  );

  const submitForm = async (e) => {
    e.preventDefault();
    if (validator.allValid()) {
      if (value.password !== value.confirm_password) {
        toast.error("Passwords do not match!");
        return;
      }

      setIsSubmitting(true);

      try {
        // Format the date to ISO string (YYYY-MM-DD)
        const formattedDate = new Date(value.dateNaissance)
          .toISOString()
          .split("T")[0];

        // Create the student registration request
        const studentData = {
          firstName: value.firstName,
          lastName: value.lastName,
          email: value.email,
          username: value.username || value.email, // Use provided username or fallback to email
          phoneNumber: value.phoneNumber,
          password: value.password,
          dateNaissance: formattedDate,
          niveauId: parseInt(value.niveauId, 10),
          abonnementTypeId: parseInt(value.abonnementTypeId, 10),
          status: "PENDING",
          role: "etudiant",
          enabled: false, // Account will be enabled when admin approves
        };

        // Send the registration request to the backend
        await axios.post(
          "http://localhost:8084/api/registration/etudiant",
          studentData
        );

        // Reset the form
        setValue({
          email: "",
          username: "",
          firstName: "",
          lastName: "",
          phoneNumber: "",
          password: "",
          confirm_password: "",
          dateNaissance: "",
          niveauId: "",
          abonnementTypeId: "",
        });

        validator.hideMessages();
        toast.success(
          "Registration submitted successfully! Your account will be reviewed by an administrator."
        );

        // Redirect to home page after successful registration
        setTimeout(() => {
          push("/");
        }, 1500);
      } catch (error) {
        console.error("Registration error:", error);
        // Log more detailed error information
        if (error.response) {
          console.error("Error response:", {
            data: error.response.data,
            status: error.response.status,
            headers: error.response.headers,
          });
          toast.error(
            `Registration failed: ${
              error.response.data.message ||
              error.response.data ||
              "Please try again later."
            }`
          );
        } else if (error.request) {
          console.error("Error request:", error.request);
          toast.error(
            "Unable to reach the server. Please check your connection."
          );
        } else {
          console.error("Error message:", error.message);
          toast.error("Registration failed. Please try again later.");
        }
      } finally {
        setIsSubmitting(false);
      }
    } else {
      validator.showMessages();
      toast.error("Please correct the errors in the form!");
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "#EEF9F5",
        backgroundSize: "cover",
        backgroundPosition: "center",
        padding: "40px 0",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          left: 0,
          top: 0,
          width: "100%",
          height: "100%",
          background: "linear-gradient(135deg, #EEF9F5 0%, #F6F4EE 100%)",
          opacity: 0.8,
        },
      }}
    >
      {/* Decorative elements */}
      <Box className="bg-pattern" />

      <Box
        className="decorative-circle"
        sx={{
          top: "15%",
          left: "10%",
          width: "300px",
          height: "300px",
          animationDelay: "0s",
        }}
      />

      <Box
        className="decorative-circle"
        sx={{
          bottom: "10%",
          right: "5%",
          width: "200px",
          height: "200px",
          animationDelay: "1s",
        }}
      />

      <Box
        className="decorative-circle"
        sx={{
          top: "60%",
          left: "15%",
          width: "150px",
          height: "150px",
          animationDelay: "2s",
        }}
      />

      <Box
        className="decorative-circle"
        sx={{
          top: "20%",
          right: "15%",
          width: "100px",
          height: "100px",
          animationDelay: "1.5s",
        }}
      />

      {/* Additional decorative elements */}
      <Box
        className="decorative-square"
        sx={{
          bottom: "25%",
          right: "25%",
          width: "80px",
          height: "80px",
          transform: "rotate(45deg)",
          animationDelay: "0.5s",
        }}
      />

      <Box
        className="decorative-square"
        sx={{
          top: "35%",
          left: "25%",
          width: "60px",
          height: "60px",
          transform: "rotate(15deg)",
          animationDelay: "1.2s",
        }}
      />
      <Container maxWidth="md" sx={{ position: "relative", zIndex: 1 }}>
        <Paper
          elevation={3}
          sx={{
            borderRadius: "16px",
            overflow: "hidden",
            boxShadow: "0 8px 30px rgba(0, 0, 0, 0.1)",
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(242, 188, 0, 0.1)",
          }}
        >
          <Grid container>
            {/* Left side - Form */}
            <Grid
              item
              xs={12}
              md={8}
              sx={{
                padding: { xs: "30px 20px", md: "40px" },
                backgroundColor: "#fff",
              }}
            >
              <Box sx={{ mb: 4, textAlign: "center" }}>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    color: "#000080",
                    mb: 1,
                    fontSize: { xs: "28px", md: "36px" },
                    textTransform: "capitalize",
                    position: "relative",
                    display: "inline-block",
                    "&::after": {
                      content: '""',
                      position: "absolute",
                      bottom: "-10px",
                      left: "50%",
                      transform: "translateX(-50%)",
                      width: "80px",
                      height: "3px",
                      background: "#F2BC00",
                    },
                  }}
                >
                  <Link
                    to="/home"
                    style={{ textDecoration: "none", color: "#F2BC00" }}
                  >
                    <Box
                      component="span"
                      sx={{
                        display: "inline-flex",
                        alignItems: "center",
                        mr: 1,
                        cursor: "pointer",
                        transition: "transform 0.3s",
                        "&:hover": { transform: "translateX(-3px)" },
                      }}
                    >
                      ⇦
                    </Box>
                  </Link>
                  Inscription des étudiants{" "}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#1D1D1B",
                    fontSize: { xs: "16px", md: "18px" },
                    mt: 3,
                  }}
                >
                  Créez votre compte pour commencer
                </Typography>
              </Box>

              <form onSubmit={submitForm}>
                <Grid container spacing={2}>
                  {/* Left Column */}
                  <Grid item xs={12} md={6}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder="Prénom"
                          value={value.firstName}
                          variant="outlined"
                          name="firstName"
                          label="Prénom"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "firstName",
                          value.firstName,
                          "required|alpha"
                        )}
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder="Nom"
                          value={value.lastName}
                          variant="outlined"
                          name="lastName"
                          label="Nom"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "lastName",
                          value.lastName,
                          "required|alpha"
                        )}
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder="Nom d’utilisateur"
                          value={value.username}
                          variant="outlined"
                          name="username"
                          label="Nom d’utilisateur"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "username",
                          value.username,
                          "required|alpha_num"
                        )}
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder="E-mail"
                          value={value.email}
                          variant="outlined"
                          name="email"
                          label="E-mail"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "email",
                          value.email,
                          "required|email"
                        )}
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder="Mot de passe"
                          value={value.password}
                          variant="outlined"
                          name="password"
                          label="Mot de passe"
                          type="password"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "password",
                          value.password,
                          "required|min:6"
                        )}
                      </Grid>
                    </Grid>
                  </Grid>

                  {/* Right Column */}
                  <Grid item xs={12} md={6}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder="Numéro de téléphone"
                          value={value.phoneNumber}
                          variant="outlined"
                          name="phoneNumber"
                          label="Numéro de téléphone"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "phoneNumber",
                          value.phoneNumber,
                          "required"
                        )}
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          type="date"
                          value={value.dateNaissance}
                          variant="outlined"
                          name="dateNaissance"
                          label="Date de naissance"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "dateNaissance",
                          value.dateNaissance,
                          "required"
                        )}
                      </Grid>
                      <Grid item xs={12}>

                      <FormControl fullWidth className="niveauSelect" variant="outlined"
                      >
  <InputLabel id="niveau-label">Niveau d'étude</InputLabel>
  <Select
    labelId="niveau-label"
    name="niveauId"
    value={value.niveauId}
    onChange={changeHandler}
    onBlur={changeHandler}
    label="Niveau d'étude"
  >
    {niveaux.map((niveau) => (
      <MenuItem key={niveau.id} value={niveau.id}>
        {niveau.nom || niveau.nomNiveau}
      </MenuItem>
    ))}
  </Select>
</FormControl>
</Grid>

                      <Grid item xs={12}>
                     
                        <FormControl
                         fullWidth className="niveauSelect" >

                           <InputLabel id="abonnement-label">
                            Type d'abonnement
                          </InputLabel>
                          <Select
                            className="inputOutline"
                            labelId="abonnement-label"
                            value={value.abonnementTypeId}
                            name="abonnementTypeId"
                            onChange={(e) => changeHandler(e)}
                          >
                            {abonnements.map((abonnement) => (
                              <MenuItem
                                key={abonnement.id}
                                value={abonnement.id}
                              >
                                {abonnement.nom} - {abonnement.prix} TND
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        {validator.message(
                          "abonnementTypeId",
                          value.abonnementTypeId,
                          "required"
                        )}
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          className="inputOutline"
                          fullWidth
                          placeholder=" Confirmer le mot de passe"
                          value={value.confirm_password}
                          variant="outlined"
                          name="confirm_password"
                          label=" Confirmer le mot de passe"
                          type="password"
                          InputLabelProps={{ shrink: true }}
                          onBlur={(e) => changeHandler(e)}
                          onChange={(e) => changeHandler(e)}
                        />
                        {validator.message(
                          "confirm password",
                          value.confirm_password,
                          "required|min:6"
                        )}
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Button
                      fullWidth
                      variant="contained"
                      type="submit"
                      disabled={isSubmitting}
                      className="theme-btn"
                      sx={{
                        py: 1.8,
                        backgroundColor: "#F2BC00",
                        "&:hover": {
                          backgroundColor: "#e0ad00",
                        },
                        borderRadius: "30px",
                        fontWeight: 700,
                        fontSize: "16px",
                        textTransform: "capitalize",
                        letterSpacing: "0.5px",
                        boxShadow: "0 4px 15px rgba(242, 188, 0, 0.4)",
                        transition: "all 0.3s ease",
                        position: "relative",
                        overflow: "hidden",
                        color: "#000080",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: "100%",
                          background:
                            "linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%)",
                          zIndex: 1,
                        },
                      }}
                    >
                      {isSubmitting ? (
                        <>
                          <CircularProgress
                            size={24}
                            sx={{ color: "#000080", mr: 1 }}
                          />
                          Envoi en cours…{" "}
                        </>
                      ) : (
                        " Commencez dès aujourd’hui"
                      )}
                    </Button>

                  
                    {/**  <Box
                      sx={{
                        mt: 3,
                        textAlign: "center",
                        position: "relative",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: "50%",
                          left: 0,
                          width: "100%",
                          height: "1px",
                          background: "rgba(0,0,0,0.1)",
                          zIndex: 0,
                        },
                      }}
                    > <Typography
                        variant="body2"
                        sx={{
                          color: "#1D1D1B",
                          display: "inline-block",
                          background: "#fff",
                          position: "relative",
                          zIndex: 1,
                          padding: "0 15px",
                          fontWeight: 500,
                        }}
                      >
                        Vous avez déjà un compte ?{" "}
                      </Typography>
                    </Box> */}  
                  </Grid>
                </Grid>
              </form>
            </Grid>

            {/* Right side - Info */}
            <Grid
              item
              md={4}
              sx={{
                display: { xs: "none", md: "flex" },
                flexDirection: "column",
                justifyContent: "center",
                padding: "40px 30px",
                background: "linear-gradient(135deg, #F2BC00 0%, #e0ad00 100%)",
                color: "#1D1D1B",
                position: "relative",
                overflow: "hidden",
                "&::before": {
                  content: '""',
                  position: "absolute",
                  top: "-50%",
                  right: "-50%",
                  width: "200%",
                  height: "200%",
                  background:
                    "radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)",
                  zIndex: 0,
                },
              }}
            >
              <Box sx={{ position: "relative", zIndex: 1 }}>
                <Typography
                  variant="h5"
                  sx={{
                    mb: 3,
                    fontWeight: 700,
                    fontSize: "28px",
                    textTransform: "capitalize",
                    position: "relative",
                    display: "inline-block",
                    color: "#000080",
                    "&::after": {
                      content: '""',
                      position: "absolute",
                      bottom: "-10px",
                      left: 0,
                      width: "50px",
                      height: "3px",
                      background: "#000080",
                    },
                  }}
                >
                  Rejoignez notre communauté d’apprentissage
                </Typography>

                <Box
                  sx={{ mt: 5, mb: 2, display: "flex", alignItems: "center" }}
                >
                  <Box
                    sx={{
                      width: "24px",
                      height: "24px",
                      borderRadius: "50%",
                      background: "rgba(36, 142, 57, 0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 2,
                    }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        background: "#248E39",
                      }}
                    />
                  </Box>
                  <Typography
                    variant="body1"
                    sx={{ fontSize: "16px", fontWeight: 600, color: "#1D1D1B" }}
                  >
                    Accédez à des cours de qualité
                  </Typography>
                </Box>

                <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
                  <Box
                    sx={{
                      width: "24px",
                      height: "24px",
                      borderRadius: "50%",
                      background: "rgba(36, 142, 57, 0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 2,
                    }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        background: "#248E39",
                      }}
                    />
                  </Box>
                  <Typography
                    variant="body1"
                    sx={{ fontSize: "16px", fontWeight: 600, color: "#1D1D1B" }}
                  >
                    Apprenez auprès d’enseignants experts
                  </Typography>
                </Box>

                <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
                  <Box
                    sx={{
                      width: "24px",
                      height: "24px",
                      borderRadius: "50%",
                      background: "rgba(36, 142, 57, 0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 2,
                    }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        background: "#248E39",
                      }}
                    />
                  </Box>
                  <Typography
                    variant="body1"
                    sx={{ fontSize: "16px", fontWeight: 600, color: "#1D1D1B" }}
                  >
                    Planning d’apprentissage flexible
                  </Typography>
                </Box>

                <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
                  <Box
                    sx={{
                      width: "24px",
                      height: "24px",
                      borderRadius: "50%",
                      background: "rgba(36, 142, 57, 0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 2,
                    }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        background: "#248E39",
                      }}
                    />
                  </Box>
                  <Typography
                    variant="body1"
                    sx={{ fontSize: "16px", fontWeight: 600, color: "#1D1D1B" }}
                  >
                    Outils d’apprentissage interactifs
                  </Typography>
                </Box>

                <Divider sx={{ my: 4, backgroundColor: "rgba(0,0,128,0.2)" }} />

                <Box
                  sx={{
                    p: 3,
                    backgroundColor: "rgba(255,255,255,0.3)",
                    borderRadius: "8px",
                    border: "1px solid rgba(0,0,128,0.2)",
                    position: "relative",
                    "&::before": {
                      content: '""',
                      position: "absolute",
                      top: 0,
                      left: 0,
                      width: "100%",
                      height: "100%",
                      background:
                        "linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%)",
                      zIndex: -1,
                    },
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontStyle: "italic",
                      lineHeight: 1.6,
                      color: "#000080",
                      fontWeight: 500,
                    }}
                  >
                    <strong>Note:</strong> Après votre inscription, votre compte
                    devra être approuvé par un administrateur avant que vous
                    puissiez vous connecter. Vous serez informé dès que votre
                    compte sera approuvé.
                  </Typography>
                </Box>
              </Box>

              {/* Decorative elements */}
              <Box
                sx={{
                  position: "absolute",
                  top: "10%",
                  right: "10%",
                  width: "50px",
                  height: "50px",
                  borderRadius: "50%",
                  background: "rgba(255,255,255,0.1)",
                  zIndex: 0,
                }}
              />

              <Box
                sx={{
                  position: "absolute",
                  bottom: "15%",
                  left: "5%",
                  width: "30px",
                  height: "30px",
                  borderRadius: "50%",
                  background: "rgba(255,255,255,0.1)",
                  zIndex: 0,
                }}
              />
            </Grid>
          </Grid>
        </Paper>
      </Container>
    </Box>
  );
};

export default SignUpPage;
