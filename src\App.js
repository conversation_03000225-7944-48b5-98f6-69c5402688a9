import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import LoginPage from "./main-component/LoginPage";
import HomePage from "./main-component/HomePage"; // Ensure this page exists
// Import SignUpPage directly from the file, not from the folder
import SignUpPage from "./main-component/SignUpPage/index.js";
import Layout from "./main-component/Layout";
import CoursePage from "./main-component/CoursePage/CoursePage";
// Import AbonnementDetailPage
import AbonnementDetailPage from "./main-component/AbonnementDetailPage/AbonnementDetailPage";
// Import TestPage
import TestPage from "./main-component/TestPage/TestPage";

function App() {
  return (
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/home" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignUpPage />} />
            <Route path="/course" element={<CoursePage />} />
            {/* Route for abonnement details */}
            <Route path="abonnement/:id" element={<AbonnementDetailPage />} />
            {/* Route for test page */}
            <Route path="/test" element={<TestPage />} />
          </Routes>
        </Layout>
      </Router>
  );
}

export default App;
