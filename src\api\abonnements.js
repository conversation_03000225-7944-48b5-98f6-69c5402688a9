// Mock data for abonnements
const abonnements = [
  {
    id: 1,
    nom: "Abonnement Basic",
    description: "Accès à toutes les matières de base pour les débutants",
    prix: 29.99,
    duree: 1, // 1 mois
    imageAbonnement: {
      idImage: 1,
      name: "basic.jpg",
      type: "image/jpeg"
    },
    matieres: [
      {
        idMatiere: 1,
        nomMatiere: "Mathématiques",
        description: "Cours de mathématiques niveau débutant",
        duree: 10
      },
      {
        idMatiere: 2,
        nomMatiere: "Français",
        description: "Cours de français niveau débutant",
        duree: 8
      }
    ]
  },
  {
    id: 2,
    nom: "Abonnement Standard",
    description: "Accès à toutes les matières avec support limité",
    prix: 49.99,
    duree: 3, // 3 mois
    imageAbonnement: {
      idImage: 2,
      name: "standard.jpg",
      type: "image/jpeg"
    },
    matieres: [
      {
        idMatiere: 1,
        nomMatiere: "Mathématiques",
        description: "Cours de mathématiques niveau intermédiaire",
        duree: 15
      },
      {
        idMatiere: 2,
        nomMatiere: "Français",
        description: "Cours de français niveau intermédiaire",
        duree: 12
      },
      {
        idMatiere: 3,
        nomMatiere: "Anglais",
        description: "Cours d'anglais niveau intermédiaire",
        duree: 10
      }
    ]
  },
  {
    id: 3,
    nom: "Abonnement Premium",
    description: "Accès illimité à toutes les matières avec support prioritaire",
    prix: 99.99,
    duree: 12, // 12 mois
    imageAbonnement: {
      idImage: 3,
      name: "premium.jpg",
      type: "image/jpeg"
    },
    matieres: [
      {
        idMatiere: 1,
        nomMatiere: "Mathématiques",
        description: "Cours de mathématiques niveau avancé",
        duree: 20
      },
      {
        idMatiere: 2,
        nomMatiere: "Français",
        description: "Cours de français niveau avancé",
        duree: 18
      },
      {
        idMatiere: 3,
        nomMatiere: "Anglais",
        description: "Cours d'anglais niveau avancé",
        duree: 15
      },
      {
        idMatiere: 4,
        nomMatiere: "Sciences",
        description: "Cours de sciences niveau avancé",
        duree: 22
      },
      {
        idMatiere: 5,
        nomMatiere: "Informatique",
        description: "Cours d'informatique niveau avancé",
        duree: 25
      }
    ]
  }
];

export const getAllAbonnements = () => {
  return abonnements;
};

export const getAbonnementById = (id) => {
  const abonnement = abonnements.find(abonnement => abonnement.id === parseInt(id));
  if (!abonnement) {
    console.error(`Abonnement avec l'ID ${id} non trouvé dans les données mock`);
  }
  return abonnement;
};

export default abonnements;
