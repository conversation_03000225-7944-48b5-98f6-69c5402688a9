import React from 'react'
import { Link } from 'react-router-dom'
import Team from '../../api/team'
import shape1 from '../../images/team/shape-1.svg'
import shape2 from '../../images/team/shape-2.svg'
import shape3 from '../../images/team/shape-3.svg'
import shape4 from '../../images/team/shape-4.svg'

const ClickHandler = () => {
    window.scrollTo(10, 0);
}

const TeamSection = (props) => {

    // Fonction pour extraire les initiales du nom complet
    const getInitials = (name) => {
        return name
            .split(' ')
            .map(word => word.charAt(0).toUpperCase())
            .join('')
            .substring(0, 2); // Prendre seulement les 2 premières initiales
    };

    // Fonction pour générer une couleur de fond basée sur le nom
    const getAvatarColor = (name) => {
        const colors = [
            '#FF6B6B', // Rouge corail
            '#4ECDC4', // Turquoise
            '#45B7D1', // Bleu ciel
            '#96CEB4', // Vert menthe
            '#FFEAA7', // Jaune doux
            '#DDA0DD', // Violet clair
            '#98D8C8', // Vert aqua
            '#F7DC6F', // Jaune doré
            '#FF8A80', // Rouge clair
            '#80CBC4', // Turquoise clair
            '#81C784', // Vert clair
            '#FFB74D'  // Orange doux
        ];

        // Utiliser le code ASCII du premier caractère pour choisir une couleur
        const index = name.charCodeAt(0) % colors.length;
        return colors[index];
    };

    return (
        <section className={`wpo-team-section section-padding ${props.pbClass}`}>
            <div className="container">
                <div className="wpo-section-title-s2">
                    <small>Notre Enseignements</small>
                    <h2>Découvrez nos
                        <span>
                        Experts en enseignement                            <i className="shape">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 206 53" fill="none">
                                    <path
                                        d="M152.182 2.58319C107.878 0.889793 54.8748 6.13932 21.2281 18.6943C14.2699 21.4407 7.93951 24.7738 5.70192 28.7128C4.27488 31.2398 5.03121 33.954 7.69121 36.2925C14.8835 42.3911 31.9822 45.4011 46.8006 47.3115C78.3067 51.0179 113.672 51.7406 145.489 48.3204C167.194 46.0009 200.667 39.5923 199.399 28.5709C198.543 20.0621 180.437 14.5729 162.979 11.6178C138.219 7.469 111.131 6.00576 84.5743 5.86862C71.32 5.85789 58.0913 6.85723 45.6675 8.78436C33.512 10.7186 21.2709 13.4317 12.6602 17.5817C11.2246 18.2877 8.62449 17.4553 9.9973 16.6813C20.7486 11.2493 38.0215 7.73493 53.9558 5.76368C77.1194 2.90994 101.75 3.75426 125.339 5.14356C158.167 7.2615 207.554 13.5139 204.928 30.7413C203.629 36.0898 194.762 40.5057 184.681 43.5503C156.563 51.768 119.114 53.6844 85.6331 52.5265C65.1694 51.7477 44.4831 50.1855 25.9972 46.2442C11.4129 43.1186 -1.0337 37.8297 0.0679738 30.5063C2.14003 19.9035 31.4913 12.0006 52.6201 7.98775C71.2971 4.45904 91.3384 2.2302 111.674 1.24636C125.228 0.595237 138.962 0.539188 152.536 1.15931C153.475 1.20224 154.154 1.55523 154.051 1.94876C153.951 2.33872 153.115 2.62135 152.182 2.58319Z" />
                                </svg>
                            </i>
                        </span>
                    </h2>
                </div>
                <div className="wpo-team-wrap">
                    <div className="row">
                    {Team.map((team, aitem) => (
                            <div className="col col-lg-3 col-md-6 col-12" key={aitem}>
                            <div className="wpo-team-item">
                                <div className="wpo-team-img">
                                    <div className="wpo-team-img-box">
                                        {/* Avatar avec initiales remplaçant l'image */}
                                        <div
                                            style={{
                                                // Prendre toute la largeur et hauteur du conteneur parent
                                                width: "100%",
                                                height: "100%",
                                                minHeight: "250px", // Hauteur minimale pour un bel affichage
                                                // Bordure arrondie pour un style moderne
                                                borderRadius: "15px",
                                                // Couleur de fond générée automatiquement basée sur le nom
                                                backgroundColor: getAvatarColor(team.name),
                                                // Centrer le contenu horizontalement et verticalement
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                flexDirection: "column",
                                                // Style du texte des initiales
                                                color: "#fff", // Texte blanc pour contraster avec le fond coloré
                                                fontSize: "48px", // Taille plus grande pour les initiales
                                                fontWeight: "bold", // Texte en gras
                                                fontFamily: "Arial, sans-serif", // Police claire et lisible
                                                // Ombre portée pour donner de la profondeur
                                                boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                                                // Transition fluide pour les animations
                                                transition: "transform 0.3s ease, box-shadow 0.3s ease",
                                                // Position relative pour les éléments enfants
                                                position: "relative",
                                                // Curseur pointer pour indiquer l'interactivité
                                                cursor: "pointer"
                                            }}
                                            // Événement déclenché quand la souris entre sur l'avatar
                                            onMouseEnter={(e) => {
                                                // Léger agrandissement et ombre plus prononcée au survol
                                                e.target.style.transform = "scale(1.05)";
                                                e.target.style.boxShadow = "0 12px 35px rgba(0,0,0,0.25)";
                                            }}
                                            // Événement déclenché quand la souris quitte l'avatar
                                            onMouseLeave={(e) => {
                                                // Remet l'avatar à sa taille et ombre normales
                                                e.target.style.transform = "scale(1)";
                                                e.target.style.boxShadow = "0 8px 25px rgba(0,0,0,0.15)";
                                            }}
                                        >
                                            {/* Affichage des initiales extraites du nom */}
                                            <div style={{ marginBottom: "10px" }}>
                                                {getInitials(team.name)}
                                            </div>

                                            {/* Nom complet en plus petit sous les initiales */}
                                            <div style={{
                                                fontSize: "14px",
                                                fontWeight: "normal",
                                                opacity: "0.9",
                                                textAlign: "center",
                                                maxWidth: "80%"
                                            }}>
                                                {team.name}
                                            </div>
                                        </div>

                                        {/* Icônes de réseaux sociaux repositionnées */}
                                        <ul style={{
                                            position: "absolute",
                                            bottom: "15px",
                                            left: "50%",
                                            transform: "translateX(-50%)",
                                            display: "flex",
                                            gap: "10px",
                                            listStyle: "none",
                                            margin: "0",
                                            padding: "0"
                                        }}>
                                            <li>
                                                <Link
                                                    onClick={ClickHandler}
                                                    to="/"
                                                    style={{
                                                        display: "inline-block",
                                                        padding: "8px",
                                                        backgroundColor: "rgba(255,255,255,0.2)",
                                                        borderRadius: "50%",
                                                        color: "#fff",
                                                        transition: "background-color 0.3s ease"
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.backgroundColor = "rgba(255,255,255,0.3)";
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.backgroundColor = "rgba(255,255,255,0.2)";
                                                    }}
                                                >
                                                    <i className="fi flaticon-facebook-app-symbol"></i>
                                                </Link>
                                            </li>
                                            <li>
                                                <Link
                                                    onClick={ClickHandler}
                                                    to="/"
                                                    style={{
                                                        display: "inline-block",
                                                        padding: "8px",
                                                        backgroundColor: "rgba(255,255,255,0.2)",
                                                        borderRadius: "50%",
                                                        color: "#fff",
                                                        transition: "background-color 0.3s ease"
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.backgroundColor = "rgba(255,255,255,0.3)";
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.backgroundColor = "rgba(255,255,255,0.2)";
                                                    }}
                                                >
                                                    <i className="fi flaticon-twitter"></i>
                                                </Link>
                                            </li>
                                            <li>
                                                <Link
                                                    onClick={ClickHandler}
                                                    to="/"
                                                    style={{
                                                        display: "inline-block",
                                                        padding: "8px",
                                                        backgroundColor: "rgba(255,255,255,0.2)",
                                                        borderRadius: "50%",
                                                        color: "#fff",
                                                        transition: "background-color 0.3s ease"
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.backgroundColor = "rgba(255,255,255,0.3)";
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.backgroundColor = "rgba(255,255,255,0.2)";
                                                    }}
                                                >
                                                    <i className="fi flaticon-linkedin"></i>
                                                </Link>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div className="wpo-team-text">
                                    <h2><Link onClick={ClickHandler} to={`/team-single/${team.slug}`}>{team.name}</Link></h2>
                                    <span>{team.title}</span>
                                </div>
                            </div>
                        </div>
                        ))}

                    </div>
                </div>
            </div>
            <div className="shape-1"><img src={shape1} alt=""/></div>
            <div className="shape-2"><img src={shape2} alt=""/></div>
            <div className="shape-3"><img src={shape3} alt=""/></div>
            <div className="shape-4"><img src={shape4} alt=""/></div>
        </section>
    )
}

export default TeamSection;