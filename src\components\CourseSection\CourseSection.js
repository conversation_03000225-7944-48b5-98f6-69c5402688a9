import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import Logo from "../../images/deutzaaa.png";
import { getAllAbonnements } from "../../api/abonnements";
import "./CourseSection.css"; // Import du fichier CSS pour les styles personnalisés

const ClickHandler = () => {
  window.scrollTo(10, 0);
};

const CourseSection = (props) => {
  const [abonnements, setAbonnements] = useState([]); // État pour stocker les abonnements
  const [loading, setLoading] = useState(true); // État pour indiquer le chargement
  const [error, setError] = useState(null); // État pour stocker les erreurs

  useEffect(() => {
    // Essayer d'abord de récupérer les données depuis l'API
    setLoading(true);
    fetch("http://localhost:8084/api/abonnements/all")
      .then((response) => {
        if (!response.ok) {
          throw new Error("API non disponible");
        }
        return response.json();
      })
      .then((data) => {
        console.log("Données récupérées:", data); // Log pour déboguer
        setAbonnements(data);
        setLoading(false);
      })
      .catch((error) => {
        console.log(
          "Erreur lors de la récupération des données:",
          error.message
        );
        setError(error.message);
        // Utiliser les données mock si l'API n'est pas disponible
        const mockData = getAllAbonnements();
        setAbonnements(mockData);
        setLoading(false);
      });
  }, []);

  // Fonction pour convertir l'image en base64 en URL
  const getImageUrl = (imageData) => {
    if (!imageData || !imageData.image) return Logo;
    try {
      // Si l'image est déjà une URL, la retourner
      if (
        typeof imageData.image === "string" &&
        (imageData.image.startsWith("http") ||
          imageData.image.startsWith("data:"))
      ) {
        return imageData.image;
      }
      // Sinon, convertir l'image en base64
      return `data:${imageData.type};base64,${imageData.image}`;
    } catch (error) {
      console.error("Erreur lors de la conversion de l'image:", error);
      return Logo;
    }
  };

  return (
    <div className={`wpo-popular-area section-padding ${props.pClass}`}>
      <div className="container">
        <div className="wpo-section-title-s2">
          <small>Nos Abonnements</small>
          <h2>
            Choisissez votre
            <span>
              Abonnement
              <i className="shape">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 206 53"
                  fill="none"
                >
                  <path d="M152.182 2.58319C107.878 0.889793 54.8748 6.13932 21.2281 18.6943C14.2699 21.4407 7.93951 24.7738 5.70192 28.7128C4.27488 31.2398 5.03121 33.954 7.69121 36.2925C14.8835 42.3911 31.9822 45.4011 46.8006 47.3115C78.3067 51.0179 113.672 51.7406 145.489 48.3204C167.194 46.0009 200.667 39.5923 199.399 28.5709C198.543 20.0621 180.437 14.5729 162.979 11.6178C138.219 7.469 111.131 6.00576 84.5743 5.86862C71.32 5.85789 58.0913 6.85723 45.6675 8.78436C33.512 10.7186 21.2709 13.4317 12.6602 17.5817C11.2246 18.2877 8.62449 17.4553 9.9973 16.6813C20.7486 11.2493 38.0215 7.73493 53.9558 5.76368C77.1194 2.90994 101.75 3.75426 125.339 5.14356C158.167 7.2615 207.554 13.5139 204.928 30.7413C203.629 36.0898 194.762 40.5057 184.681 43.5503C156.563 51.768 119.114 53.6844 85.6331 52.5265C65.1694 51.7477 44.4831 50.1855 25.9972 46.2442C11.4129 43.1186 -1.0337 37.8297 0.0679738 30.5063C2.14003 19.9035 31.4913 12.0006 52.6201 7.98775C71.2971 4.45904 91.3384 2.2302 111.674 1.24636C125.228 0.595237 138.962 0.539188 152.536 1.15931C153.475 1.20224 154.154 1.55523 154.051 1.94876C153.951 2.33872 153.115 2.62135 152.182 2.58319Z" />
                </svg>
              </i>
            </span>
          </h2>
        </div>

        {loading ? (
          <div className="text-center">
            <p>Chargement des abonnements...</p>
          </div>
        ) : error ? (
          <div className="text-center">
            <p>Erreur lors du chargement des abonnements: {error}</p>
          </div>
        ) : (
          <div className="wpo-popular-wrap">
            <div className="row">
              {abonnements && abonnements.length > 0 ? (
                abonnements.map((abonnement, index) => (
                  <div className="col col-lg-4 col-md-6 col-12" key={index}>
                    <div className="wpo-popular-single">
                      <div className="wpo-popular-item">
                        <div className="wpo-popular-img">
                          <img
                            src={
                              abonnement.imageAbonnement
                                ? getImageUrl(abonnement.imageAbonnement)
                                : Logo
                            }
                            alt={abonnement.nom}
                          />
                          <div className="thumb">
                            <span>{abonnement.prix} DT</span>
                          </div>
                        </div>
                        <div className="wpo-popular-content carte-abonnement d-flex flex-column">
                          <h2>
                            <Link
                              onClick={ClickHandler}
                              to={`/abonnement/${abonnement.id}`}
                            >
                              {abonnement.nom}
                            </Link>
                          </h2>
                          <p className="card-description">{abonnement.description}</p>

                          {/* Afficher la durée de l'abonnement */}
                          {abonnement.duree && (
                            <p className="duration">
                              <strong>Durée:</strong> {abonnement.duree}{" "}
                              {abonnement.duree > 1 ? "mois" : "mois"}
                            </p>
                          )}

                          {/* Afficher les matières associées si disponibles */}
                          {abonnement.matieres &&
                            abonnement.matieres.length > 0 && (
                              <div className="subjects">
                                <strong>Matières incluses:</strong>
                                <ul>
                                  {abonnement.matieres.map((matiere, idx) => (
                                    <li key={idx}>
                                      {matiere.nomMatiere || matiere.nom}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}

                          <div className="more-btn text-center mt-auto">
                            <Link
                              onClick={ClickHandler}
                              to="/register"
                              className="theme-btn-s2"
                              state={{ abonnement: abonnement }}
                            >
                              S'inscrire
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-12 text-center">
                  <p>Aucun abonnement disponible pour le moment.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseSection;
