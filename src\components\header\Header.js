import React, { useState } from "react";
import { Link } from "react-router-dom";
import MobileMenu from "../MobileMenu/MobileMenu";
import Logo from "../../images/deutza.png";
import HeaderTopbar from "../HeaderTopbar/HeaderTopbar";
import { useKeycloak } from "@react-keycloak/web";
import HeaderImage from "../../images/deutzaaa_blanc__1_-removebg-preview.png"; // Chemin relatif correct
import logo from "../../images/logo1.png";
import "../../style.css"

const Header = (props) => {
  const { keycloak } = useKeycloak();

  const [menuActive, setMenuState] = useState(false);
  const SubmitHandler = (e) => {
    e.preventDefault();
  };

  const ClickHandler = () => {
    window.scrollTo(10, 0);
  };

  return (
    <header className="header shadow-sm" style={{ backgroundColor: 'var(--header-bg)' }}>
    <HeaderTopbar topbarClass={props.topbarClass} />
  
    <div className={`wpo-site-header ${props.hclass}`}>
      <nav className="navigation navbar navbar-expand-lg navbar-light">
        <div className="container-fluid d-flex align-items-center justify-content-between py-3">
          
          {/* Logo */}
          <div className="navbar-header d-flex align-items-center"  style={{
    marginLeft: '60px',   // réduit si 90px est trop espacé
    marginBottom: '0px',  // colle plus le div suivant si besoin
  }} >
            <Link onClick={ClickHandler} className="navbar-brand" to="/home">
              <img
                src={HeaderImage}
                alt="logo"
                style={{ width: '130px', height: 'auto'  }}
                className="logo-image"

              />
            </Link>
          </div>
  
          {/* Mobile Menu */}
          <div className="d-lg-none">
            <MobileMenu />
          </div>
  
          {/* Navigation Links */}
          <div className="collapse navbar-collapse justify-content-center d-none d-lg-flex" id="navbar">
            <ul className="navbar-nav d-flex gap-4 align-items-center mb-0">
              <li className="nav-item">
                <Link className="nav-link menu-link" to="/" onClick={ClickHandler}>
                  Accueil
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link menu-link" to="/about" onClick={ClickHandler}>
                  À propos
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link menu-link" to="/course" onClick={ClickHandler}>
                  Matières
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link menu-link" to="/teacher" onClick={ClickHandler}>
                  Enseignants
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link menu-link" to="/blog-fullwidth" onClick={ClickHandler}>
                  Blog
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link menu-link" to="/contact" onClick={ClickHandler}>
                  Contact
                </Link>
              </li>
              {!keycloak.authenticated && (
                <li className="nav-item">
                  <Link className="nav-link menu-link" to="/register" onClick={ClickHandler}>
                    Inscription
                  </Link>
                </li>
              )}
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </header>
  
  );
};

export default Header;
