import axios from 'axios';
import keycloak from '../keycloak';

const axiosInstance = axios.create({
  baseURL: 'http://localhost:8084',
  //timeout: "",
  //withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});
/*const axiosInstance = axios.create({
  baseURL: 'http://localhost:8084',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});*/

// Request interceptor
axiosInstance.interceptors.request.use(
  async (config) => {
    if (keycloak.authenticated) {
      try {
        await keycloak.updateToken(30);
        config.headers.Authorization = `Bearer ${keycloak.token}`;
      } catch (error) {
        console.error('Failed to refresh token:', error);
        await keycloak.logout();
        return Promise.reject(error);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (!error.response) {
      console.error('Network error:', error);
      return Promise.reject(new Error('Erreur de connexion au serveur. Veuillez vérifier votre connexion.'));
    }

    if (error.response?.status === 401) {
      try {
        await keycloak.updateToken(30);
        const originalRequest = error.config;
        originalRequest.headers.Authorization = `Bearer ${keycloak.token}`;
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        await keycloak.logout();
        return Promise.reject(new Error('Session expirée. Veuillez vous reconnecter.'));
      }
    }

    const errorMessage = error.response?.data?.message || error.message || 'Une erreur est survenue';
    console.error('API Error:', errorMessage);
    return Promise.reject(new Error(errorMessage));
  }
);

export default axiosInstance;