import React, { useEffect, useState } from "react";
import { Link, useParams, useLocation } from "react-router-dom";
import Logo from '../../images/deutzaaa.png';
import "./CourseSectionS3.css";

const API_URL = "http://localhost:8084/api/matieres"; // URL de base pour toutes les matières
const ABONNEMENT_API_URL = "http://localhost:8084/api/abonnements"; // URL pour les abonnements

const CourseSectionS3 = (props) => {
    const { id } = useParams(); // Récupérer l'ID de l'abonnement depuis l'URL si disponible
    const location = useLocation();
    const [matieres, setMatieres] = useState([]);
    const [abonnement, setAbonnement] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        setLoading(true);

        // Si un ID d'abonnement est fourni dans les props ou l'URL, charger les matières de cet abonnement
        if (props.abonnementId || id) {
            const abonnementId = props.abonnementId || id;

            // Si l'abonnement est passé dans l'état de la route
            if (location.state && location.state.abonnement) {
                console.log("Utilisation des données de l'état:", location.state.abonnement);
                setAbonnement(location.state.abonnement);
                if (location.state.abonnement.matieres && location.state.abonnement.matieres.length > 0) {
                    setMatieres(location.state.abonnement.matieres);
                    setLoading(false);
                    return;
                }
            }

            // Sinon, charger les matières de l'abonnement depuis l'API
            fetch(`${ABONNEMENT_API_URL}/${abonnementId}/matieres`)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Erreur lors du chargement des matières de l'abonnement");
                    }
                    return response.json();
                })
                .then((data) => {
                    console.log("Matières de l'abonnement récupérées:", data);
                    setMatieres(data);
                    setLoading(false);
                })
                .catch((err) => {
                    console.error("Erreur:", err);
                    setError(err.message);
                    setLoading(false);
                });
        } else {
            // Si aucun ID d'abonnement n'est fourni, charger toutes les matières
            fetch(API_URL)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Erreur lors du chargement des données");
                    }
                    return response.json();
                })
                .then((data) => {
                    setMatieres(data);
                    setLoading(false);
                })
                .catch((err) => {
                    console.error("Erreur:", err);
                    setError(err.message);
                    setLoading(false);
                });
        }
    }, [props.abonnementId, id, location.state]);

    if (loading) return (
        <div className="container mt-5 text-center">
            <div className="spinner-border" role="status">
                <span className="sr-only">Chargement...</span>
            </div>
            <p className="mt-3">Chargement des matières...</p>
        </div>
    );

    if (error) return (
        <div className="container mt-5 text-center">
            <div className="alert alert-danger" role="alert">
                {error}
            </div>
        </div>
    );

    return (
        <div className={`wpo-popular-area section-padding ${props.pClass}`}>
            <div className="container">
                {/* Titre de la section avec le nom de l'abonnement si disponible */}
                <div className="wpo-section-title-s2 text-center">
                    <small>Nos Matières</small>
                    <h2>
                        {abonnement ? `Matières incluses dans ${abonnement.nom}` : "Toutes nos matières"}
                        <span>
                            Disponibles
                            <i className="shape">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 206 53"
                                    fill="none"
                                >
                                                              <path d="M152.182 2.58319C107.878 0.889793 54.8748 6.13932 21.2281 18.6943C14.2699 21.4407 7.93951 24.7738 5.70192 28.7128C4.27488 31.2398 5.03121 33.954 7.69121 36.2925C14.8835 42.3911 31.9822 45.4011 46.8006 47.3115C78.3067 51.0179 113.672 51.7406 145.489 48.3204C167.194 46.0009 200.667 39.5923 199.399 28.5709C198.543 20.0621 180.437 14.5729 162.979 11.6178C138.219 7.469 111.131 6.00576 84.5743 5.86862C71.32 5.85789 58.0913 6.85723 45.6675 8.78436C33.512 10.7186 21.2709 13.4317 12.6602 17.5817C11.2246 18.2877 8.62449 17.4553 9.9973 16.6813C20.7486 11.2493 38.0215 7.73493 53.9558 5.76368C77.1194 2.90994 101.75 3.75426 125.339 5.14356C158.167 7.2615 207.554 13.5139 204.928 30.7413C203.629 36.0898 194.762 40.5057 184.681 43.5503C156.563 51.768 119.114 53.6844 85.6331 52.5265C65.1694 51.7477 44.4831 50.1855 25.9972 46.2442C11.4129 43.1186 -1.0337 37.8297 0.0679738 30.5063C2.14003 19.9035 31.4913 12.0006 52.6201 7.98775C71.2971 4.45904 91.3384 2.2302 111.674 1.24636C125.228 0.595237 138.962 0.539188 152.536 1.15931C153.475 1.20224 154.154 1.55523 154.051 1.94876C153.951 2.33872 153.115 2.62135 152.182 2.58319Z" />

                                </svg>
                            </i>
                        </span>
                    </h2>
                </div>

                {matieres && matieres.length > 0 ? (
                    <div className="wpo-popular-wrap">
                        <div className="row">
                            {matieres.map((matiere, index) => (
                                <div className="col col-lg-4 col-md-6 col-12" key={index}>
                                    <div className="wpo-popular-single">
                                        <div className="wpo-popular-item">
                                            <div className="wpo-popular-img">
                                                <img
                                                    src={matiere.image ? `data:${matiere.image.type};base64,${matiere.image.image}` : Logo}
                                                    alt={matiere.nomMatiere || matiere.nom}
                                                />
                                            </div>
                                            <div className="wpo-popular-content">
                                                <h2>{matiere.nomMatiere || matiere.nom}</h2>
                                                <p>{matiere.description}</p>
                                                <ul className="matiere-details">
                                                    {matiere.duree && (
                                                        <li>⏳ Durée : {matiere.duree} heures</li>
                                                    )}
                                                    {matiere.nomDeProf && (
                                                        <li>👨‍🏫 Professeur : {matiere.nomDeProf}</li>
                                                    )}
                                                </ul>
                                            {/**v <div className="more-btn text-center">
                                                    <Link
                                                        to={`/matiere/${matiere.idMatiere || matiere.id}`}
                                                        className="theme-btn-s2"
                                                    >
                                                        Voir Détails
                                                    </Link>
                                                </div> */}   
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                ) : (
                    <div className="no-matieres text-center">
                        <p>Aucune matière disponible pour le moment.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CourseSectionS3;
