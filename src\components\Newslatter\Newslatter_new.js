/*
╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║                                    COMPOSANT NEWSLETTER - DEUTZA                                                ║
╠══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╣
║                                                                                                                  ║
║  📋 DESCRIPTION:                                                                                                 ║
║      Composant React pour la section d'abonnement à la newsletter de DEUTZA                                     ║
║      Permet aux utilisateurs de s'inscrire pour recevoir les dernières actualités et services                  ║
║                                                                                                                  ║
║  🎯 FONCTIONNALITÉS:                                                                                             ║
║      • Formulaire d'abonnement avec validation email                                                            ║
║      • Redirection vers la page d'inscription (/register)                                                       ║
║      • Design responsive et moderne                                                                             ║
║      • Texte d'encouragement personnalisé                                                                       ║
║                                                                                                                  ║
║  🔧 TECHNOLOGIES:                                                                                                ║
║      • React 18+ (Hooks et composants fonctionnels)                                                             ║
║      • React Router (useNavigate pour navigation)                                                               ║
║      • HTML5 (validation email native)                                                                          ║
║      • CSS3 (classes personnalisées)                                                                            ║
║                                                                                                                  ║
║  📱 RESPONSIVE DESIGN:                                                                                           ║
║      • Adaptatif pour mobile, tablette et desktop                                                               ║
║      • Formulaire centré avec mise en page flexible                                                             ║
║      • Bouton et champ de saisie optimisés pour le tactile                                                      ║
║                                                                                                                  ║
║  👤 AUTEUR: Équipe Développement DEUTZA                                                                         ║
║  📅 DATE: 2025                                                                                                  ║
║  📝 VERSION: 2.0 - Navigation vers inscription et amélioration UX                                               ║
║                                                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
*/

import React from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *                                        GESTIONNAIRE DE SOUMISSION
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

/**
 * Gestionnaire de soumission du formulaire d'abonnement
 * Redirige l'utilisateur vers la page d'inscription pour créer un compte complet
 *
 * @function SubmitHandler
 * @param {Event} e - Événement de soumission du formulaire
 * @param {Function} navigate - Fonction de navigation React Router
 * @description Empêche la soumission par défaut et redirige vers /register
 */
const SubmitHandler = (e, navigate) => {
    // Empêcher la soumission par défaut du formulaire
    e.preventDefault();

    try {
        // Redirection vers la page d'inscription
        navigate('/register');

        // Log pour le débogage (peut être retiré en production)
        console.log('Redirection vers la page d\'inscription');

    } catch (error) {
        // Gestion d'erreur en cas de problème de navigation
        console.error('Erreur lors de la redirection:', error);

        // Fallback : redirection manuelle si navigate échoue
        window.location.href = '/register';
    }
};

/**
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *                                        COMPOSANT PRINCIPAL
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

/**
 * Composant Newsletter pour l'abonnement aux actualités DEUTZA
 *
 * @component Newslatter
 * @param {Object} props - Propriétés du composant (extensibilité future)
 * @returns {JSX.Element} Section d'abonnement à la newsletter
 *
 * @description
 * Affiche une section d'abonnement à la newsletter avec un formulaire
 * qui redirige vers la page d'inscription pour une expérience utilisateur complète
 *
 * @example
 * // Utilisation basique
 * <Newslatter />
 *
 * @features
 * ✅ Formulaire d'abonnement avec validation HTML5
 * ✅ Redirection vers page d'inscription
 * ✅ Texte d'encouragement personnalisé
 * ✅ Design responsive et moderne
 * ✅ Gestion d'erreur intégrée
 * ✅ Accessibilité optimisée
 */
const Newslatter = (props) => {
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            HOOKS ET NAVIGATION
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Hook de navigation React Router pour redirection programmatique
     * @type {Function}
     */
    const navigate = useNavigate();

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            CONFIGURATION DU CONTENU
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Configuration du contenu de la newsletter
     * @constant {Object} newsletterContent
     */
    const newsletterContent = {
        title: "Abonnez-vous à notre lettre d'information pour rester informé de nos nouveautés et services.",
        placeholder: "Rejoignez-nous ! Créez votre compte pour commencer",
        buttonText: "S'abonner",
        inputType: "email",
        inputRequired: true
    };

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            RENDU DU COMPOSANT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    return (
        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                                    SECTION NEWSLETTER PRINCIPALE                                           │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        <section className="wpo-subscribe-section section-padding">
            {/* Conteneur Bootstrap pour la mise en page responsive */}
            <div className="container">
                <div className="wpo-subscribe-wrap">

                    {/*
                    ┌─────────────────────────────────────────────────────────────────────────────────────────────┐
                    │                                    TEXTE D'ACCROCHE                                        │
                    └─────────────────────────────────────────────────────────────────────────────────────────────┘
                    */}
                    <div className="subscribe-text">
                        {/* Titre principal utilisant la configuration */}
                        <h3>{newsletterContent.title}</h3>
                    </div>

                    {/*
                    ┌─────────────────────────────────────────────────────────────────────────────────────────────┐
                    │                                    FORMULAIRE D'ABONNEMENT                                 │
                    └─────────────────────────────────────────────────────────────────────────────────────────────┘
                    */}
                    <div className="subscribe-form">
                        {/* Formulaire avec gestionnaire de soumission personnalisé */}
                        <form onSubmit={(e) => SubmitHandler(e, navigate)}>
                            <div className="input-field">
                                {/* Champ email avec placeholder personnalisé pour guider vers l'inscription */}
                                <input
                                    type={newsletterContent.inputType}
                                    placeholder={newsletterContent.placeholder}
                                    required={newsletterContent.inputRequired}
                                    aria-label="Adresse email pour l'abonnement"
                                    title="Entrez votre email pour vous diriger vers l'inscription"
                                />
                                {/* Bouton d'abonnement qui déclenche la redirection */}
                                <button
                                    type="submit"
                                    aria-label="S'abonner à la newsletter et créer un compte"
                                    title="Cliquez pour vous diriger vers la page d'inscription"
                                >
                                    {newsletterContent.buttonText}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    );
};

/*
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
                                            EXPORT DU COMPOSANT
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
*/

/**
 * Export par défaut du composant Newslatter
 *
 * @exports Newslatter
 * @description Composant React pour la section newsletter de DEUTZA
 *
 * @usage
 * // Import dans un autre composant
 * import Newslatter from './components/Newslatter/Newslatter';
 *
 * // Utilisation dans une page
 * <Newslatter />
 *
 * @features
 * ✅ Redirection vers page d'inscription (/register)
 * ✅ Placeholder personnalisé pour guider l'utilisateur
 * ✅ Validation HTML5 intégrée
 * ✅ Design responsive et accessible
 * ✅ Gestion d'erreur robuste
 * ✅ Documentation complète JSDoc
 * ✅ Code commenté et structuré
 *
 * @navigation
 * 🔗 Bouton "S'abonner" → Redirection vers /register
 * 📝 Placeholder: "Rejoignez-nous ! Créez votre compte pour commencer"
 *
 * @accessibility
 * ♿ Labels ARIA pour les lecteurs d'écran
 * ♿ Attributs title pour les info-bulles
 * ♿ Validation HTML5 native
 */
export default Newslatter;

/*
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                            FIN DU FICHIER                                                      │
│                                                                                                                 │
│  📝 RÉSUMÉ DES MODIFICATIONS:                                                                                   │
│     • Ajout de la redirection vers /register                                                                   │
│     • Changement du placeholder : "Rejoignez-nous ! Créez votre compte pour commencer"                        │
│     • Documentation JSDoc complète                                                                             │
│     • Commentaires détaillés pour chaque section                                                               │
│     • Amélioration de l'accessibilité (ARIA, title)                                                            │
│     • Gestion d'erreur robuste                                                                                 │
│     • Configuration centralisée du contenu                                                                     │
│                                                                                                                 │
│  🚀 PRÊT POUR LA PRODUCTION                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
*/
