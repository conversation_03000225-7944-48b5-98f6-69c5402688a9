<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - DEUTZA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Variables CSS pour une meilleure maintenance */
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: var(--background-gradient);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* Animation de particules en arrière-plan */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            z-index: -1;
        }

        .fix-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
            max-width: 450px;
            width: 100%;
        }

        /* Effet de brillance sur la carte */
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .card:hover::before {
            left: 100%;
        }

        .card-body {
            padding: 48px 40px;
            position: relative;
        }

        .logo-container {
            margin-bottom: 32px;
            text-align: center;
        }

        .logo-container svg {
            filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
        }

        .welcome-text {
            text-align: center;
            margin-bottom: 32px;
        }

        .welcome-text h4 {
            color: #1f2937;
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .welcome-text p {
            color: var(--secondary-color);
            font-size: 0.95rem;
            margin: 0;
        }

        .form-floating {
            position: relative;
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 12px;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            transition: var(--transition);
            font-size: 0.95rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 0.95);
            outline: none;
        }

        .form-control::placeholder {
            color: #9ca3af;
            transition: var(--transition);
        }

        .form-control:focus::placeholder {
            opacity: 0;
            transform: translateY(-10px);
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            padding-right: 50px;
        }

        .btn-primary {
            border-radius: 12px;
            padding: 16px 24px;
            font-weight: 600;
            font-size: 0.95rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border: none;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--secondary-color);
            transition: var(--transition);
            z-index: 10;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 24px 0;
            font-size: 0.9rem;
        }

        .form-check {
            display: flex;
            align-items: center;
        }

        .form-check-input {
            margin-right: 8px;
            border-radius: 4px;
            border: 2px solid #d1d5db;
            transition: var(--transition);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            color: var(--secondary-color);
            cursor: pointer;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .forgot-password:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .error-message {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-left: 4px solid var(--error-color);
            padding: 16px;
            margin-bottom: 24px;
            border-radius: 8px;
            color: #991b1b;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .error-message i {
            margin-right: 8px;
            color: var(--error-color);
        }

        .register-link {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .register-link a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
            z-index: 5;
        }

        .form-control.with-icon {
            padding-left: 48px;
        }

        /* Animation de chargement */
        .loading {
            position: relative;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive design */
        @media (max-width: 576px) {
            .card-body {
                padding: 32px 24px;
            }
            
            .welcome-text h4 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
<div class="fix-wrapper">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <div class="card">
                    <div class="card-body">
                        <!-- Logo et titre -->
                        <div class="logo-container">
                            <svg width="250" height="56" viewBox="0 0 250 56">
                                <g transform="translate(20, 10)">
                                    <text x="70" y="35" font-size="32" font-family="'Inter', sans-serif" font-weight="bold" fill="#2563eb">
                                        DEUTZA
                                    </text>
                                </g>
                                <image class="image" href="${url.resourcesPath}/img/logo1.png" x="0" y="-7" width="80" height="70" />
                            </svg>
                        </div>

                        <!-- Message de bienvenue -->
                        <div class="welcome-text">
                            <h4>Bon retour !</h4>
                            <p>Connectez-vous à votre compte pour continuer</p>
                        </div>

                        <!-- Message d'erreur -->
                        <#if message?has_content && message.type = "error">
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                ${message.summary}
                            </div>
                        </#if>

                        <!-- Formulaire de connexion -->
                        <form id="kc-form-login" action="${url.loginAction}" method="post">
                            <!-- Champ Email -->
                            <div class="form-floating mb-3">
                                <div class="input-group">
                                    <i class="fas fa-envelope input-icon"></i>
                                    <input tabindex="1" id="username" class="form-control with-icon" 
                                           name="username" value="${login.username!''}" 
                                           placeholder="Entrez votre adresse email" required autofocus>
                                </div>
                            </div>

                            <!-- Champ Mot de passe -->
                            <div class="form-floating mb-3">
                                <div class="input-group">
                                    <i class="fas fa-lock input-icon"></i>
                                    <input tabindex="2" id="password" class="form-control with-icon" 
                                           name="password" type="password" 
                                           placeholder="Entrez votre mot de passe" required>
                                    <i class="far fa-eye password-toggle" id="togglePassword"></i>
                                </div>
                            </div>

                            <!-- Options Se souvenir / Mot de passe oublié -->
                            <div class="remember-forgot">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="rememberMe" name="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Se souvenir de moi
                                    </label>
                                </div>
                                <a href="${url.loginResetCredentialsUrl}" class="forgot-password">
                                    Mot de passe oublié ?
                                </a>
                            </div>

                            <!-- Bouton de connexion -->
                            <div class="d-grid">
                                <button tabindex="4" class="btn btn-primary" 
                                        name="login" id="kc-login" type="submit">
                                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                                </button>
                            </div>

                            <!-- Lien d'inscription -->
                            <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
                                <div class="register-link">
                                    <span>Vous n'avez pas de compte ? </span>
                                    <a href="${url.registrationUrl}">
                                        Créer un compte
                                    </a>
                                </div>
                            </#if>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Basculer la visibilité du mot de passe
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.classList.toggle('fa-eye');
        this.classList.toggle('fa-eye-slash');
    });

    // Animation de chargement sur le bouton de soumission
    document.getElementById('kc-form-login').addEventListener('submit', function() {
        const button = document.getElementById('kc-login');
        button.classList.add('loading');
        button.innerHTML = '<span style="opacity: 0;">Se connecter</span>';
    });

    // Animation d'entrée pour la carte
    window.addEventListener('load', function() {
        const card = document.querySelector('.card');
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);
    });
</script>

</body>
</html>
