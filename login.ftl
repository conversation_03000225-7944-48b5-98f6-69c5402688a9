<!--
╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║                                    INTERFACE DE CONNEXION KEYCLOAK - DEUTZA                                     ║
╠══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╣
║                                                                                                                  ║
║  📋 DESCRIPTION:                                                                                                 ║
║      Template Keycloak personnalisé pour l'interface de connexion de la plateforme DEUTZA                      ║
║      Intègre la charte graphique officielle avec des animations modernes et un design responsive               ║
║                                                                                                                  ║
║  🎨 CHARTE DE COULEURS:                                                                                          ║
║      • Or primaire: #A7DF (éléments principaux)                                                                 ║
║      • Jaune secondaire: #F2BC00 (accents et highlights)                                                        ║
║      • Gris neutre: #B7B7B7 (textes secondaires)                                                                ║
║      • Menthe claire: #EEF9F5 (arrière-plans doux)                                                              ║
║      • Vert succès: #248E39 (boutons d'action)                                                                  ║
║      • Charbon foncé: #1D1D1B (textes principaux)                                                               ║
║      • Bleu marine: #000080 (éléments de contraste)                                                             ║
║      • Blanc crème: #F6F4EE (arrière-plans principaux)                                                          ║
║                                                                                                                  ║
║  ✨ FONCTIONNALITÉS:                                                                                             ║
║      • Animations fluides d'entrée et d'interaction                                                             ║
║      • Particules animées en arrière-plan                                                                       ║
║      • Effets de glassmorphism et backdrop-filter                                                               ║
║      • Design responsive (mobile-first)                                                                         ║
║      • Accessibilité améliorée (ARIA, focus, contraste)                                                         ║
║      • Validation visuelle en temps réel                                                                        ║
║      • Bouton de visibilité du mot de passe                                                                     ║
║                                                                                                                  ║
║  🔧 TECHNOLOGIES:                                                                                                ║
║      • Keycloak FreeMarker Template (.ftl)                                                                      ║
║      • Bootstrap 5.3.0 (framework CSS)                                                                          ║
║      • Font Awesome 6.0.0 (icônes)                                                                              ║
║      • Google Fonts Inter (typographie)                                                                         ║
║      • CSS3 avancé (animations, gradients, filters)                                                             ║
║      • JavaScript vanilla (interactions)                                                                        ║
║                                                                                                                  ║
║  📱 COMPATIBILITÉ:                                                                                               ║
║      • Navigateurs modernes (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)                                    ║
║      • Responsive design (320px à 1920px+)                                                                      ║
║      • Support des écrans haute résolution (Retina, 4K)                                                         ║
║                                                                                                                  ║
║  👤 AUTEUR: Équipe Développement DEUTZA                                                                         ║
║  📅 DATE: 2025                                                                                                  ║
║  📝 VERSION: 2.0                                                                                                ║
║                                                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
-->

<!DOCTYPE html>
<html lang="fr">
<head>
    <!-- Configuration de base du document -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - DEUTZA</title>

    <!-- Frameworks et bibliothèques externes -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /*
        ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
                                                VARIABLES CSS GLOBALES
        ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
        */
        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                                    CHARTE DE COULEURS DEUTZA                                              │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        :root {
            /* Couleurs principales de la marque DEUTZA */
            --primary-gold: #A7DF;          /* Or primaire - Utilisé pour les éléments de marque */
            --secondary-yellow: #F2BC00;    /* Jaune secondaire - Accents et highlights */
            --neutral-gray: #B7B7B7;        /* Gris neutre - Textes secondaires et bordures */
            --light-mint: #EEF9F5;          /* Menthe claire - Arrière-plans doux */
            --success-green: #248E39;       /* Vert succès - Boutons d'action et validation */
            --dark-charcoal: #1D1D1B;       /* Charbon foncé - Textes principaux */
            --navy-blue: #000080;           /* Bleu marine - Éléments de contraste */
            --cream-white: #F6F4EE;         /* Blanc crème - Arrière-plans principaux */
            --error-color: #ef4444;         /* Rouge erreur - Messages d'erreur */

            /*
            ┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐
            │                                    VARIABLES D'ANIMATION                                              │
            └─────────────────────────────────────────────────────────────────────────────────────────────────────┘
            */
            /* Transitions fluides avec courbes de Bézier personnalisées */
            --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);    /* Transition douce */
            --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);  /* Effet rebond */

            /* Ombres portées avec profondeur progressive */
            --shadow-soft: 0 15px 50px rgba(29, 29, 27, 0.1);      /* Ombre douce pour état normal */
            --shadow-hover: 0 25px 70px rgba(29, 29, 27, 0.15);    /* Ombre prononcée pour survol */

            /* Rayon de bordure uniforme */
            --border-radius: 20px;          /* Bordures arrondies modernes */
        }

        /*
        ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
                                            STYLES DE BASE ET RESET CSS
        ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
        */

        /* Reset CSS universel pour une cohérence cross-browser */
        * {
            box-sizing: border-box;    /* Inclut padding et border dans la largeur/hauteur */
        }

        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                                    STYLES DU BODY PRINCIPAL                                               │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        body {
            /* Dégradé de fond utilisant la charte de couleurs DEUTZA */
            background: linear-gradient(135deg, var(--light-mint) 0%, var(--cream-white) 50%, var(--neutral-gray) 100%);
            min-height: 100vh;                    /* Hauteur minimale plein écran */

            /* Typographie moderne et lisible */
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

            /* Reset des marges et gestion du débordement */
            margin: 0;
            padding: 0;
            overflow-x: hidden;                   /* Empêche le scroll horizontal */
            position: relative;                   /* Contexte pour les éléments positionnés */
        }

        /*
        ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
                                            ANIMATIONS D'ARRIÈRE-PLAN
        ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
        */

        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                              COUCHE 1: DÉGRADÉS FLOTTANTS                                                 │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        /* Pseudo-élément ::before pour créer des dégradés radiaux animés */
        body::before {
            content: '';                          /* Contenu vide requis pour les pseudo-éléments */
            position: fixed;                      /* Position fixe pour rester en place lors du scroll */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            /* Superposition de dégradés radiaux utilisant la charte DEUTZA */
            background-image:
                radial-gradient(circle at 20% 80%, rgba(242, 188, 0, 0.15) 0%, transparent 50%),  /* Jaune en bas-gauche */
                radial-gradient(circle at 80% 20%, rgba(36, 142, 57, 0.1) 0%, transparent 50%),   /* Vert en haut-droite */
                radial-gradient(circle at 40% 40%, rgba(0, 0, 128, 0.08) 0%, transparent 50%),    /* Bleu au centre */
                radial-gradient(circle at 60% 60%, rgba(167, 223, 255, 0.1) 0%, transparent 50%); /* Or clair décalé */

            animation: backgroundFloat 20s ease-in-out infinite;  /* Animation de flottement */
            z-index: -1;                          /* Derrière tout le contenu */
        }

        /* Animation de flottement pour les dégradés d'arrière-plan */
        @keyframes backgroundFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);    /* Position initiale */
                opacity: 1;                                 /* Opacité normale */
            }
            33% {
                transform: translateY(-20px) rotate(1deg);  /* Mouvement vers le haut avec rotation */
                opacity: 0.8;                               /* Légère transparence */
            }
            66% {
                transform: translateY(10px) rotate(-1deg);  /* Mouvement vers le bas avec rotation inverse */
                opacity: 0.9;                               /* Retour progressif de l'opacité */
            }
        }

        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                              COUCHE 2: PARTICULES DISCRÈTES                                               │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        /* Pseudo-élément ::after pour créer des particules colorées */
        body::after {
            content: '';                          /* Contenu vide requis */
            position: fixed;                      /* Position fixe */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            /* Motif de particules utilisant les couleurs DEUTZA */
            background-image:
                radial-gradient(2px 2px at 20px 30px, var(--secondary-yellow), transparent),  /* Particule jaune */
                radial-gradient(2px 2px at 40px 70px, var(--success-green), transparent),     /* Particule verte */
                radial-gradient(1px 1px at 90px 40px, var(--navy-blue), transparent),         /* Particule bleue */
                radial-gradient(1px 1px at 130px 80px, var(--secondary-yellow), transparent), /* Particule jaune */
                radial-gradient(2px 2px at 160px 30px, var(--success-green), transparent);    /* Particule verte */

            background-repeat: repeat;            /* Répétition du motif */
            background-size: 200px 100px;        /* Taille du motif répété */
            animation: particlesMove 25s linear infinite;  /* Animation de déplacement */
            opacity: 0.6;                        /* Transparence pour subtilité */
            z-index: -1;                          /* Derrière le contenu */
        }

        /* Animation de déplacement des particules */
        @keyframes particlesMove {
            0% {
                transform: translateY(0px) translateX(0px);     /* Position de départ */
            }
            100% {
                transform: translateY(-100px) translateX(50px); /* Déplacement diagonal vers le haut */
            }
        }

        .fix-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            backdrop-filter: blur(25px);
            background: linear-gradient(145deg, rgba(246, 244, 238, 0.95), rgba(238, 249, 245, 0.9));
            border: 2px solid rgba(242, 188, 0, 0.3);
            overflow: hidden;
            position: relative;
            max-width: 450px;
            width: 100%;
            transform: translateY(30px) scale(0.95);
            opacity: 0;
            animation: cardEntrance 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.3s forwards;
        }

        @keyframes cardEntrance {
            0% {
                transform: translateY(30px) scale(0.95);
                opacity: 0;
            }
            50% {
                transform: translateY(-10px) scale(1.02);
                opacity: 0.8;
            }
            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        /* Effet de brillance dorée sur la carte */
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(242, 188, 0, 0.2), transparent);
            transition: left 0.8s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-8px) scale(1.02);
            transition: var(--transition-smooth);
            border-color: rgba(242, 188, 0, 0.5);
        }

        .card:hover::before {
            left: 100%;
        }

        .card-body {
            padding: 48px 40px;
            position: relative;
        }

        .logo-container {
            margin-bottom: 32px;
            text-align: center;
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
            }
            50% {
                transform: translateY(-8px) scale(1.05);
            }
        }

        .logo-container svg {
            filter: drop-shadow(0 6px 12px rgba(29, 29, 27, 0.15));
        }

        .welcome-text {
            text-align: center;
            margin-bottom: 32px;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-text h4 {
            color: var(--dark-charcoal);
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: 8px;
            background: linear-gradient(135deg, var(--dark-charcoal), var(--navy-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-text p {
            color: var(--neutral-gray);
            font-size: 0.95rem;
            margin: 0;
        }

        .form-floating {
            position: relative;
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 12px;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            transition: var(--transition);
            font-size: 0.95rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 0.95);
            outline: none;
        }

        .form-control::placeholder {
            color: #9ca3af;
            transition: var(--transition);
        }

        .form-control:focus::placeholder {
            opacity: 0;
            transform: translateY(-10px);
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            padding-right: 50px;
        }

        .btn-primary {
            border-radius: 12px;
            padding: 16px 24px;
            font-weight: 600;
            font-size: 0.95rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border: none;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--secondary-color);
            transition: var(--transition);
            z-index: 10;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 24px 0;
            font-size: 0.9rem;
        }

        .form-check {
            display: flex;
            align-items: center;
        }

        .form-check-input {
            margin-right: 8px;
            border-radius: 4px;
            border: 2px solid #d1d5db;
            transition: var(--transition);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            color: var(--secondary-color);
            cursor: pointer;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .forgot-password:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .error-message {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-left: 4px solid var(--error-color);
            padding: 16px;
            margin-bottom: 24px;
            border-radius: 8px;
            color: #991b1b;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .error-message i {
            margin-right: 8px;
            color: var(--error-color);
        }

        .register-link {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .register-link a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
            z-index: 5;
        }

        .form-control.with-icon {
            padding-left: 48px;
        }

        /* Animation de chargement */
        .loading {
            position: relative;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive design */
        @media (max-width: 576px) {
            .card-body {
                padding: 32px 24px;
            }

            .welcome-text h4 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
<div class="fix-wrapper">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <div class="card">
                    <div class="card-body">
                        <!-- Logo et titre -->
                        <div class="logo-container">
                            <svg width="250" height="56" viewBox="0 0 250 56">
                                <g transform="translate(20, 10)">
                                    <text x="70" y="35" font-size="32" font-family="'Inter', sans-serif" font-weight="bold" fill="#2563eb">
                                        DEUTZA
                                    </text>
                                </g>
                                <image class="image" href="${url.resourcesPath}/img/logo1.png" x="0" y="-7" width="80" height="70" />
                            </svg>
                        </div>

                        <!-- Message de bienvenue -->
                        <div class="welcome-text">
                            <h4>Bon retour !</h4>
                            <p>Connectez-vous à votre compte pour continuer</p>
                        </div>

                        <!-- Message d'erreur -->
                        <#if message?has_content && message.type = "error">
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                ${message.summary}
                            </div>
                        </#if>

                        <!-- Formulaire de connexion -->
                        <form id="kc-form-login" action="${url.loginAction}" method="post">
                            <!-- Champ Email -->
                            <div class="form-floating mb-3">
                                <div class="input-group">
                                    <i class="fas fa-envelope input-icon"></i>
                                    <input tabindex="1" id="username" class="form-control with-icon"
                                           name="username" value="${login.username!''}"
                                           placeholder="Entrez votre adresse email" required autofocus>
                                </div>
                            </div>

                            <!-- Champ Mot de passe -->
                            <div class="form-floating mb-3">
                                <div class="input-group">
                                    <i class="fas fa-lock input-icon"></i>
                                    <input tabindex="2" id="password" class="form-control with-icon"
                                           name="password" type="password"
                                           placeholder="Entrez votre mot de passe" required>
                                    <i class="far fa-eye password-toggle" id="togglePassword"></i>
                                </div>
                            </div>

                            <!-- Options Se souvenir / Mot de passe oublié -->
                            <div class="remember-forgot">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="rememberMe" name="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Se souvenir de moi
                                    </label>
                                </div>
                                <a href="${url.loginResetCredentialsUrl}" class="forgot-password">
                                    Mot de passe oublié ?
                                </a>
                            </div>

                            <!-- Bouton de connexion -->
                            <div class="d-grid">
                                <button tabindex="4" class="btn btn-primary"
                                        name="login" id="kc-login" type="submit">
                                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                                </button>
                            </div>

                            <!-- Lien d'inscription -->
                            <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
                                <div class="register-link">
                                    <span>Vous n'avez pas de compte ? </span>
                                    <a href="${url.registrationUrl}">
                                        Créer un compte
                                    </a>
                                </div>
                            </#if>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
                                            SCRIPTS JAVASCRIPT
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
-->
<script>
    /*
    ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
    │                                    GESTION DE LA VISIBILITÉ DU MOT DE PASSE                                  │
    └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
    */
    // Fonction pour basculer entre affichage/masquage du mot de passe
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');                    // Récupération du champ mot de passe
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';  // Basculement du type
        password.setAttribute('type', type);                                    // Application du nouveau type

        // Basculement des icônes Font Awesome (œil ouvert/fermé)
        this.classList.toggle('fa-eye');        // Icône œil ouvert
        this.classList.toggle('fa-eye-slash');  // Icône œil barré
    });

    /*
    ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
    │                                    ANIMATION DE CHARGEMENT                                                    │
    └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
    */
    // Affichage d'un indicateur de chargement lors de la soumission du formulaire
    document.getElementById('kc-form-login').addEventListener('submit', function() {
        const button = document.getElementById('kc-login');                     // Récupération du bouton de connexion
        button.classList.add('loading');                                       // Ajout de la classe CSS de chargement
        button.innerHTML = '<span style="opacity: 0;">Se connecter</span>';    // Masquage du texte (spinner CSS prend le relais)
    });

    /*
    ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
    │                                    ANIMATION D'ENTRÉE DE LA CARTE                                             │
    └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
    */
    // Animation d'apparition progressive de la carte de connexion au chargement de la page
    window.addEventListener('load', function() {
        const card = document.querySelector('.card');                          // Sélection de la carte de connexion

        // État initial : carte invisible et décalée vers le bas
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';                              // Transition fluide

        // Animation d'entrée après un court délai
        setTimeout(() => {
            card.style.opacity = '1';                                          // Apparition progressive
            card.style.transform = 'translateY(0)';                           // Remontée à la position finale
        }, 100);                                                               // Délai de 100ms pour un effet naturel
    });

    /*
    ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
    │                                    AMÉLIORATION DE L'ACCESSIBILITÉ                                            │
    └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
    */
    // Gestion améliorée du focus pour l'accessibilité clavier
    document.addEventListener('DOMContentLoaded', function() {
        // Focus automatique sur le premier champ lors du chargement
        const firstInput = document.getElementById('username');
        if (firstInput) {
            firstInput.focus();                                                // Focus sur le champ email
        }

        // Gestion de la navigation au clavier (Enter pour passer au champ suivant)
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"]');
        inputs.forEach((input, index) => {
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && index < inputs.length - 1) {
                    e.preventDefault();                                        // Empêche la soumission prématurée
                    inputs[index + 1].focus();                                // Focus sur le champ suivant
                }
            });
        });
    });
</script>

</body>
</html>
