import React, { useEffect, useState } from "react";
import { useParams, useLocation } from "react-router-dom";
import Logo from '../../images/deutzaaa.png';
import { getAbonnementById } from "../../api/abonnements";
import "./AbonnementDetail.css";

const AbonnementDetail = () => {
    const { id } = useParams();
    const location = useLocation();
    const [abonnement, setAbonnement] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        setLoading(true);

        // Vérifier si l'abonnement est passé dans l'état de la route
        if (location.state && location.state.abonnement) {
            console.log("Utilisation des données de l'état:", location.state.abonnement);
            setAbonnement(location.state.abonnement);
            setLoading(false);
            return;
        }

        // Sino<PERSON>, essayer de récupérer les données depuis l'API
        fetch(`http://localhost:8084/api/abonnements/${id}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("API non disponible");
                }
                return response.json();
            })
            .then((data) => {
                console.log("Données de l'abonnement récupérées:", data);
                setAbonnement(data);
                setLoading(false);
            })
            .catch((error) => {
                console.log("Erreur lors de la récupération des données:", error.message);
                setError(error.message);
                // Utiliser les données mock si l'API n'est pas disponible
                const mockData = getAbonnementById(parseInt(id));
                if (mockData) {
                    setAbonnement(mockData);
                    setLoading(false);
                } else {
                    setError("Abonnement non trouvé");
                    setLoading(false);
                }
            });
    }, [id, location.state]);

    // Fonction pour convertir l'image en base64 en URL
    const getImageUrl = (imageData) => {
        if (!imageData || !imageData.image) return Logo;
        try {
            // Si l'image est déjà une URL, la retourner
            if (typeof imageData.image === 'string' && (imageData.image.startsWith('http') || imageData.image.startsWith('data:'))) {
                return imageData.image;
            }
            // Sinon, convertir l'image en base64
            return `data:${imageData.type};base64,${imageData.image}`;
        } catch (error) {
            console.error("Erreur lors de la conversion de l'image:", error);
            return Logo;
        }
    };

    if (loading) {
        return (
            <div className="container mt-5 text-center">
                <div className="spinner-border" role="status">
                    <span className="sr-only">Chargement...</span>
                </div>
                <p className="mt-3">Chargement des détails de l'abonnement...</p>
            </div>
        );
    }

    if (error || !abonnement) {
        return (
            <div className="container mt-5 text-center">
                <div className="alert alert-danger" role="alert">
                    {error || "Abonnement non trouvé"}
                </div>
            </div>
        );
    }

    return (
        <div className="abonnement-detail-section">
            <div className="container">
                <div className="row">
                    <div className="col-lg-8 offset-lg-2">
                        <div className="abonnement-header">
                            <div className="abonnement-image">
                                <img
                                    src={abonnement.imageAbonnement ? getImageUrl(abonnement.imageAbonnement) : Logo}
                                    alt={abonnement.nom}
                                />
                            </div>
                            <div className="abonnement-info">
                                <h1>{abonnement.nom}</h1>
                                <div className="abonnement-meta">
                                    <span className="price">{abonnement.prix} DT</span>
                                    {abonnement.duree && (
                                        <span className="duration">Durée: {abonnement.duree} {abonnement.duree > 1 ? 'mois' : 'mois'}</span>
                                    )}
                                </div>
                                <div className="abonnement-description">
                                    <p>{abonnement.description}</p>
                                </div>
                            </div>
                        </div>

                        {/* Section des matières */}
                        <div className="matieres-section">
                            <h2>Matières incluses dans cet abonnement</h2>
                            {abonnement.matieres && abonnement.matieres.length > 0 ? (
                                <div className="row">
                                    {abonnement.matieres.map((matiere, index) => (
                                        <div className="col-md-6" key={index}>
                                            <div className="matiere-card">
                                                <div className="matiere-icon">
                                                    <i className="fa fa-book"></i>
                                                </div>
                                                <div className="matiere-content">
                                                    <h3>{matiere.nomMatiere || matiere.nom}</h3>
                                                    <p>{matiere.description}</p>
                                                    {matiere.duree && (
                                                        <div className="matiere-meta">
                                                            <span>⏳ Durée: {matiere.duree} heures</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="no-matieres">
                                    <p>Aucune matière n'est associée à cet abonnement.</p>
                                </div>
                            )}
                        </div>

                        {/* Section d'inscription */}
                        <div className="subscription-section">
                            <div className="card">
                                <div className="card-body">
                                    <h3>S'abonner maintenant</h3>
                                    <p>Profitez de tous les avantages de cet abonnement dès aujourd'hui !</p>
                                    <button className="btn btn-primary btn-lg">S'abonner</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AbonnementDetail;
