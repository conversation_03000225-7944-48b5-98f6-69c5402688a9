/*
╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║                                    COMPOSANT NEWSLETTER - DEUTZA                                                ║
╠══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╣
║                                                                                                                  ║
║  📋 DESCRIPTION:                                                                                                 ║
║      Composant React pour la section d'abonnement à la newsletter de DEUTZA                                     ║
║      Permet aux utilisateurs de s'inscrire pour recevoir les dernières actualités et services                  ║
║                                                                                                                  ║
║  🎯 FONCTIONNALITÉS:                                                                                             ║
║      • Formulaire d'abonnement avec validation email                                                            ║
║      • Redirection vers la page d'inscription (/register)                                                       ║
║      • Design responsive et moderne                                                                             ║
║      • Texte d'encouragement personnalisé                                                                       ║
║                                                                                                                  ║
║  🔧 TECHNOLOGIES:                                                                                                ║
║      • React 18+ (Hooks et composants fonctionnels)                                                             ║
║      • React Router (useNavigate pour navigation)                                                               ║
║      • HTML5 (validation email native)                                                                          ║
║      • CSS3 (classes personnalisées)                                                                            ║
║                                                                                                                  ║
║  📱 RESPONSIVE DESIGN:                                                                                           ║
║      • Adaptatif pour mobile, tablette et desktop                                                               ║
║      • Formulaire centré avec mise en page flexible                                                             ║
║      • Bouton et champ de saisie optimisés pour le tactile                                                      ║
║                                                                                                                  ║
║  🎨 DESIGN:                                                                                                      ║
║      • Section avec padding uniforme                                                                            ║
║      • Texte d'accroche centré et engageant                                                                     ║
║      • Formulaire inline avec bouton d'action                                                                   ║
║      • Placeholder informatif pour guider l'utilisateur                                                         ║
║                                                                                                                  ║
║  👤 AUTEUR: Équipe Développement DEUTZA                                                                         ║
║  📅 DATE: 2025                                                                                                  ║
║  📝 VERSION: 2.0 - Navigation vers inscription et amélioration UX                                               ║
║                                                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
*/

import React from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *                                        GESTIONNAIRE DE SOUMISSION
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

/**
 * Gestionnaire de soumission du formulaire d'abonnement
 * Redirige l'utilisateur vers la page d'inscription pour créer un compte complet
 *
 * @function SubmitHandler
 * @param {Event} e - Événement de soumission du formulaire
 * @param {Function} navigate - Fonction de navigation React Router
 * @description Empêche la soumission par défaut et redirige vers /register
 *
 * @example
 * // Utilisation dans le formulaire
 * <form onSubmit={(e) => SubmitHandler(e, navigate)}>
 *
 * @features
 * - Prévention de la soumission par défaut
 * - Redirection programmatique vers l'inscription
 * - Gestion d'erreur intégrée
 */
const SubmitHandler = (e, navigate) => {
    // Empêcher la soumission par défaut du formulaire
    e.preventDefault();

    try {
        // Redirection vers la page d'inscription
        navigate('/register');

        // Log pour le débogage (peut être retiré en production)
        console.log('Redirection vers la page d\'inscription');

    } catch (error) {
        // Gestion d'erreur en cas de problème de navigation
        console.error('Erreur lors de la redirection:', error);

        // Fallback : redirection manuelle si navigate échoue
        window.location.href = '/register';
    }
};

/**
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *                                        COMPOSANT PRINCIPAL
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

/**
 * Composant Newsletter pour l'abonnement aux actualités DEUTZA
 *
 * @component Newslatter
 * @param {Object} props - Propriétés du composant (extensibilité future)
 * @returns {JSX.Element} Section d'abonnement à la newsletter
 *
 * @description
 * Affiche une section d'abonnement à la newsletter avec un formulaire
 * qui redirige vers la page d'inscription pour une expérience utilisateur complète
 *
 * @example
 * // Utilisation basique
 * <Newslatter />
 *
 * // Utilisation avec props (extensibilité)
 * <Newslatter className="custom-newsletter" />
 *
 * @features
 * ✅ Formulaire d'abonnement avec validation HTML5
 * ✅ Redirection vers page d'inscription
 * ✅ Texte d'encouragement personnalisé
 * ✅ Design responsive et moderne
 * ✅ Gestion d'erreur intégrée
 * ✅ Accessibilité optimisée
 */
const Newslatter = (props) => {
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            HOOKS ET NAVIGATION
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Hook de navigation React Router pour redirection programmatique
     * @type {Function}
     */
    const navigate = useNavigate();

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            CONFIGURATION DU CONTENU
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Configuration du contenu de la newsletter
     * @constant {Object} newsletterContent
     */
    const newsletterContent = {
        title: "Abonnez-vous à notre lettre d'information pour rester informé de nos nouveautés et services.",
        placeholder: "Rejoignez-nous ! Créez votre compte pour commencer",
        buttonText: "S'abonner",
        inputType: "email",
        inputRequired: true
    };

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            RENDU DU COMPOSANT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

const Newslatter = (props) => {
    return (
        <section className="wpo-subscribe-section section-padding">
            <div className="container">
                <div className="wpo-subscribe-wrap">
                    <div className="subscribe-text">
                        <h3>Abonnez-vous à notre lettre d'information pour rester informé de nos nouveautés et services.</h3>
                    </div>
                    <div className="subscribe-form">
                        <form onSubmit={SubmitHandler}>
                            <div className="input-field">
                                <input type="email" placeholder="Enter your email" required/>
                                    <button type="submit">S’abonner</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Newslatter;