import React, { useEffect, useState, useCallback, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import cImag from "../../images/shape/1.svg";
import cImag2 from "../../images/shape/2.svg";
import cImag3 from "../../images/shape/3.svg";
import cImag4 from "../../images/shape/4.svg";
import "./CategorySection.css";
import axiosInstance from "../../services/axiosInstance";
import keycloak from "../../keycloak";

const ClickHandler = () => {
  window.scrollTo(10, 0);
};

const settings = {
  dots: false,
  arrows: true,
  speed: 1000,
  slidesToShow: 3, // Afficher 3 matières à la fois puisque nous en avons 3
  slidesToScroll: 1,
  autoplay: false, // Désactiver l'autoplay pour éviter les problèmes
  infinite: false, // Désactiver l'infinite pour éviter la duplication
  pauseOnHover: true,
  centerMode: false,
  variableWidth: false,
  responsive: [
    {
      breakpoint: 1400,
      settings: {
        slidesToShow: 3,
        infinite: false
      },
    },
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
        infinite: false
      },
    },
    {
      breakpoint: 991,
      settings: {
        slidesToShow: 2,
        infinite: false
      },
    },
    {
      breakpoint: 767,
      settings: {
        slidesToShow: 1,
        infinite: false
      },
    },
  ],
};

const CategorySection = (props) => {
  const [imageUrls, setImageUrls] = useState({});
  const [matieres, setMatieres] = useState([]);
  const loadingImagesRef = useRef(new Set());

  const defaultImage = "/images/default-matiere.jpg";

  // Fonction pour obtenir une icône par défaut basée sur le nom de la matière
  const getDefaultIcon = (nomMatiere) => {
    const matiereLower = nomMatiere?.toLowerCase() || '';

    if (matiereLower.includes('math')) return '📐';
    if (matiereLower.includes('français') || matiereLower.includes('francais')) return '📚';
    if (matiereLower.includes('anglais') || matiereLower.includes('english')) return '🇬🇧';
    if (matiereLower.includes('allemand') || matiereLower.includes('german') || matiereLower.includes('deutsch')) return '🇩🇪';
    if (matiereLower.includes('science')) return '🔬';
    if (matiereLower.includes('informatique') || matiereLower.includes('programmation')) return '💻';
    if (matiereLower.includes('histoire')) return '📜';
    if (matiereLower.includes('géographie') || matiereLower.includes('geographie')) return '🌍';
    if (matiereLower.includes('physique')) return '⚛️';
    if (matiereLower.includes('chimie')) return '🧪';
    if (matiereLower.includes('biologie')) return '🧬';

    return '📖'; // Icône par défaut
  };

  const loadImage = useCallback(async (imageId) => {
    try {
      if (!imageId || loadingImagesRef.current.has(imageId)) {
        return;
      }

      // Marquer immédiatement comme en cours de chargement
      loadingImagesRef.current.add(imageId);

      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: "arraybuffer",
      });

      const blob = new Blob([response.data], {
        type: response.headers["content-type"],
      });
      const imageUrl = URL.createObjectURL(blob);

      setImageUrls(prev => {
        // Vérifier si l'image n'est pas déjà chargée
        if (prev[imageId]) {
          URL.revokeObjectURL(imageUrl); // Nettoyer la nouvelle URL si déjà présente
          return prev;
        }
        return {
          ...prev,
          [imageId]: imageUrl
        };
      });

      // Retirer de la liste de chargement
      loadingImagesRef.current.delete(imageId);
    } catch (error) {
      console.error("Error loading image:", error);
      loadingImagesRef.current.delete(imageId);
    }
  }, []);

  // Cleanup des URLs d'objets lors du démontage du composant
  useEffect(() => {
    return () => {
      Object.values(imageUrls).forEach((url) => {
        if (url && url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [imageUrls]);

  // Chargement des matières
  useEffect(() => {
    const fetchMatieres = async () => {
      try {
        console.log("Début de la récupération des matières...");
        console.log("Keycloak authenticated:", keycloak?.authenticated);
        console.log("Keycloak token:", keycloak?.token ? "Present" : "Absent");

        // Essayer d'abord avec fetch simple pour tester la connectivité
        const testResponse = await fetch("http://localhost:8084/api/matieres/all", {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (!testResponse.ok) {
          throw new Error(`HTTP error! status: ${testResponse.status}`);
        }

        const data = await testResponse.json();

        // Vérifier et dédupliquer les données si nécessaire
        const uniqueMatieres = data.filter((matiere, index, self) =>
          index === self.findIndex(m => m.id === matiere.id)
        );

        setMatieres(uniqueMatieres);
        console.log("Matières récupérées:", uniqueMatieres);
        console.log("Nombre de matières:", uniqueMatieres.length);

        // Charger les images après avoir récupéré les matières
        // Temporairement désactivé car nécessite une authentification
        console.log("Chargement des images désactivé - authentification requise");
        /*
        data.forEach(matiere => {
          if (matiere.image && matiere.image.idImage) {
            loadImage(matiere.image.idImage);
          }
        });
        */
      } catch (error) {
        console.error("Erreur lors de la récupération des matières :", error);
        console.error("Error details:", {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });

        // Afficher un message d'erreur plus détaillé
        if (error.message.includes('connexion') || error.message.includes('fetch')) {
          console.error("Vérifiez que le serveur backend est démarré sur http://localhost:8084");
          console.error("Vérifiez aussi la configuration CORS du backend");
        }
      }
    };

    fetchMatieres();
  }, [loadImage]);

  return (
    <section className={`wpo-courses-section section-padding ${props.cClass}`}>
      <div className="container">
        <div className="row">
          <div className="col-12">
            <div className="wpo-section-title-s2">
              <small>Nos Matières</small>
              <h2>
                Explorer{" "}
                <span>
                  Nos
                  <i className="shape"></i>
                </span>{" "}
                Matières              </h2>
            </div>
          </div>
        </div>
        <div className="row-grid wpo-courses-wrap wpo-courses-slider">
          {matieres.length > 0 ? (
            <Slider {...settings}>
              {matieres.map((matiere) => (
                <div className="grid" key={matiere.id}>
                  <div className="wpo-courses-item">
                    <div className="wpo-courses-text">
                      <div className="courses-icon">
                        {imageUrls[matiere.image?.idImage] ? (
                          <img
                            src={imageUrls[matiere.image.idImage]}
                            alt={matiere.nomMatiere}
                            style={{
                              width: "60px",
                              height: "60px",
                              objectFit: "cover",
                              borderRadius: "50%",
                              boxShadow: "0 2px 6px rgba(0,0,0,0.15)",
                            }}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = defaultImage;
                            }}
                          />
                        ) : (
                          <div
                            style={{
                              width: "60px",
                              height: "60px",
                              borderRadius: "50%",
                              backgroundColor: "#f0f0f0",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "24px",
                              boxShadow: "0 2px 6px rgba(0,0,0,0.15)",
                            }}
                          >
                            {getDefaultIcon(matiere.nomMatiere)}
                          </div>
                        )}
                      </div>
                      <h2>
                        <Link
                          onClick={ClickHandler}
                          to={`/matiere/${matiere.id}`}
                        >
                          {matiere.nomMatiere}
                        </Link>
                      </h2>
                      <p>{matiere.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <p>Chargement des matières...</p>
            </div>
          )}
        </div>
      </div>
      <div className="shape-1">
        <img src={cImag} alt="" />
      </div>
      <div className="shape-2">
        <img src={cImag2} alt="" />
      </div>
      <div className="shape-3">
        <img src={cImag3} alt="" />
      </div>
      <div className="shape-4">
        <img src={cImag4} alt="" />
      </div>
    </section>
  );
};

export default CategorySection;
