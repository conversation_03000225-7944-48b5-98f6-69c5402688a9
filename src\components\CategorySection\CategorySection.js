import React, { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import cImag from "../../images/shape/1.svg";
import cImag2 from "../../images/shape/2.svg";
import cImag3 from "../../images/shape/3.svg";
import cImag4 from "../../images/shape/4.svg";
import "./CategorySection.css";
import axiosInstance from "../../services/axiosInstance";

const ClickHandler = () => {
  window.scrollTo(10, 0);
};

const settings = {
  dots: false,
  arrows: true,
  speed: 1000,
  slidesToShow: 4,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 3000,
  infinite: true,
  pauseOnHover: true,
  responsive: [
    {
      breakpoint: 1400,
      settings: { slidesToShow: 4 },
    },
    {
      breakpoint: 1200,
      settings: { slidesToShow: 3 },
    },
    {
      breakpoint: 991,
      settings: { slidesToShow: 2 },
    },
    {
      breakpoint: 767,
      settings: { slidesToShow: 1 },
    },
  ],
};

const CategorySection = (props) => {
  const [imageUrls, setImageUrls] = useState({});
  const [matieres, setMatieres] = useState([]);
  const [loadingImages, setLoadingImages] = useState(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const defaultImage = "/images/default-matiere.jpg";

  const loadImage = async (imageId) => {
    try {
      if (!imageId || loadingImages.has(imageId) || imageUrls[imageId]) {
        return;
      }

      setLoadingImages(prev => new Set(prev).add(imageId));

      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: "arraybuffer",
      });

      const blob = new Blob([response.data], {
        type: response.headers["content-type"],
      });
      const imageUrl = URL.createObjectURL(blob);

      setImageUrls(prev => ({
        ...prev,
        [imageId]: imageUrl
      }));

      setLoadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(imageId);
        return newSet;
      });
    } catch (error) {
      console.error("Error loading image:", error);
      setLoadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(imageId);
        return newSet;
      });
    }
  };

  // Cleanup des URLs d'objets lors du démontage du composant
  useEffect(() => {
    return () => {
      Object.values(imageUrls).forEach((url) => {
        if (url && url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [imageUrls]);

  // Chargement des matières
  useEffect(() => {
    const fetchMatieres = async () => {
      try {
        const response = await axiosInstance.get("/api/matieres/all");
        setMatieres(response.data);
        console.log("Matières récupérées:", response.data);
      } catch (error) {
        console.error("Erreur lors de la récupération des matières :", error);
        // Afficher un message d'erreur plus détaillé
        if (error.message.includes('connexion')) {
          console.error("Vérifiez que le serveur backend est démarré sur http://localhost:8084");
        }
      }
    };

    fetchMatieres();
  }, []);

  // Chargement des images quand les matières sont disponibles
  useEffect(() => {
    if (matieres.length > 0) {
      matieres.forEach(matiere => {
        if (matiere.image && matiere.image.idImage && !imageUrls[matiere.image.idImage] && !loadingImages.has(matiere.image.idImage)) {
          loadImage(matiere.image.idImage);
        }
      });
    }
  }, [matieres, imageUrls, loadingImages]);

  return (
    <section className={`wpo-courses-section section-padding ${props.cClass}`}>
      <div className="container">
        <div className="row">
          <div className="col-12">
            <div className="wpo-section-title-s2">
              <small>Nos Matières</small>
              <h2>
                Explorer{" "}
                <span>
                  Matières
                  <i className="shape"></i>
                </span>{" "}
                Par catégorie
              </h2>
            </div>
          </div>
        </div>
        <div className="row-grid wpo-courses-wrap wpo-courses-slider owl-carousel">
          <Slider {...settings}>
            {matieres.map((matiere, index) => (
              <div className="grid" key={matiere.id || index}>
                <div className="wpo-courses-item">
                  <div className="wpo-courses-text">
                    <div className="courses-icon">
                      <img
                        src={
                          imageUrls[matiere.image?.idImage] ||
                          defaultImage
                        }
                        alt={matiere.nomMatiere}
                        style={{
                          width: "60px",
                          height: "60px",
                          objectFit: "cover",
                          borderRadius: "50%",
                          boxShadow: "0 2px 6px rgba(0,0,0,0.15)",
                        }}
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = defaultImage;
                        }}
                      />
                    </div>
                    <h2>
                      <Link
                        onClick={ClickHandler}
                        to={`/matiere/${matiere.id}`}
                      >
                        {matiere.nomMatiere}
                      </Link>
                    </h2>
                    <p>{matiere.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>
      <div className="shape-1">
        <img src={cImag} alt="" />
      </div>
      <div className="shape-2">
        <img src={cImag2} alt="" />
      </div>
      <div className="shape-3">
        <img src={cImag3} alt="" />
      </div>
      <div className="shape-4">
        <img src={cImag4} alt="" />
      </div>
    </section>
  );
};

export default CategorySection;
