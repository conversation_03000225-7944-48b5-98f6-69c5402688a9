/*
╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║                                        PAGE DE CONTACT - DEUTZA                                                 ║
╠══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╣
║                                                                                                                  ║
║  📋 DESCRIPTION:                                                                                                 ║
║      Composant React pour la page de contact de la plateforme DEUTZA                                            ║
║      Affiche les informations de contact principales : adresse, email et téléphone                              ║
║                                                                                                                  ║
║  📍 INFORMATIONS DE CONTACT:                                                                                     ║
║      • Adresse: <PERSON><PERSON><PERSON>, Tunisie                                                                                 ║
║      • Email principal: <EMAIL>                                                                      ║
║      • Téléphone: 20440595                                                                                      ║
║                                                                                                                  ║
║  🎨 STRUCTURE:                                                                                                   ║
║      • Section responsive avec 3 colonnes d'informations                                                        ║
║      • Icônes Font Awesome pour chaque type de contact                                                          ║
║      • Design adaptatif pour mobile, tablette et desktop                                                        ║
║                                                                                                                  ║
║  🔧 TECHNOLOGIES:                                                                                                ║
║      • React 18+ (Hooks: useState)                                                                              ║
║      • React Router (useNavigate pour navigation)                                                               ║
║      • Bootstrap Grid System (responsive)                                                                       ║
║      • Font Awesome Icons (flaticon)                                                                            ║
║                                                                                                                  ║
║  📱 RESPONSIVE DESIGN:                                                                                           ║
║      • XL (1200px+): 3 colonnes côte à côte                                                                     ║
║      • LG (992px+): 2 colonnes par ligne                                                                        ║
║      • MD (768px+): 2 colonnes par ligne                                                                        ║
║      • SM (576px-): 1 colonne par ligne                                                                         ║
║                                                                                                                  ║
║  👤 AUTEUR: Équipe Développement DEUTZA                                                                         ║
║  📅 DATE: 2025                                                                                                  ║
║  📝 VERSION: 2.0 - Traduction française et mise à jour des contacts                                             ║
║                                                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
*/

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *                                        COMPOSANT PAGE DE CONTACT
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * @component Contactpage
 * @description Composant principal pour afficher les informations de contact de DEUTZA
 * @returns {JSX.Element} Interface de contact avec adresse, email et téléphone
 *
 * @example
 * // Utilisation dans une route
 * <Route path="/contact" element={<Contactpage />} />
 *
 * @features
 * - Affichage responsive des informations de contact
 * - Icônes visuelles pour chaque type de contact
 * - Design adaptatif pour tous les écrans
 * - Informations de contact mises à jour pour DEUTZA
 */
const Contactpage = () => {
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            HOOKS ET ÉTAT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Hook de navigation React Router pour redirection programmatique
     * @type {Function}
     */
    const navigate = useNavigate();

    /**
     * État local pour gérer les interactions futures (extensibilité)
     * @type {[any, Function]}
     */
    const [contactState] = useState({
        // Réservé pour futures fonctionnalités (formulaire de contact, etc.)
    });

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            DONNÉES DE CONTACT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Configuration des informations de contact DEUTZA
     * @constant {Object} contactInfo
     */
    const contactInfo = {
        address: {
            title: "Adresse",
            content: ["Nabeul", "Tunisie"],
            icon: "fi flaticon-maps-and-flags"
        },
        email: {
            title: "Nous contacter par email",
            content: ["<EMAIL>"],
            icon: "fi flaticon-email"
        },
        phone: {
            title: "Appelez-nous",
            content: ["20440595"],
            icon: "fi flaticon-phone-call"
        }
    };

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            RENDU DU COMPOSANT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    return (
        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                                    SECTION PRINCIPALE DE CONTACT                                           │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        <section className="wpo-contact-pg-section section-padding">
            {/* Conteneur Bootstrap pour la mise en page responsive */}
            <div className="container">
                <div className="row">
                    {/* Colonne centrée avec offset pour un design équilibré */}
                    <div className="col col-lg-10 offset-lg-1">
                        {/* Conteneur principal des informations de contact */}
                        <div className="office-info">
                            <div className="row">

                                {/*
                                ┌─────────────────────────────────────────────────────────────────────────────────┐
                                │                                SECTION ADRESSE                                 │
                                └─────────────────────────────────────────────────────────────────────────────────┘
                                */}
                                <div className="col col-xl-4 col-lg-6 col-md-6 col-12">
                                    <div className="office-info-item">
                                        {/* Icône d'adresse avec style Font Awesome */}
                                        <div className="office-info-icon">
                                            <div className="icon">
                                                <i className={contactInfo.address.icon}></i>
                                            </div>
                                        </div>
                                        {/* Contenu textuel de l'adresse */}
                                        <div className="office-info-text">
                                            <h2>{contactInfo.address.title}</h2>
                                            <p>
                                                {contactInfo.address.content[0]} <br/>
                                                {contactInfo.address.content[1]}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/*
                                ┌─────────────────────────────────────────────────────────────────────────────────┐
                                │                                SECTION EMAIL                                   │
                                └─────────────────────────────────────────────────────────────────────────────────┘
                                */}
                                <div className="col col-xl-4 col-lg-6 col-md-6 col-12">
                                    <div className="office-info-item">
                                        {/* Icône d'email avec style Font Awesome */}
                                        <div className="office-info-icon">
                                            <div className="icon">
                                                <i className={contactInfo.email.icon}></i>
                                            </div>
                                        </div>
                                        {/* Contenu textuel de l'email */}
                                        <div className="office-info-text">
                                            <h2>{contactInfo.email.title}</h2>
                                            {/* Affichage de l'email principal DEUTZA */}
                                            <p>
                                                <a href={`mailto:${contactInfo.email.content[0]}`}>
                                                    {contactInfo.email.content[0]}
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/*
                                ┌─────────────────────────────────────────────────────────────────────────────────┐
                                │                               SECTION TÉLÉPHONE                               │
                                └─────────────────────────────────────────────────────────────────────────────────┘
                                */}
                                <div className="col col-xl-4 col-lg-6 col-md-6 col-12">
                                    <div className="office-info-item">
                                        {/* Icône de téléphone avec style Font Awesome */}
                                        <div className="office-info-icon">
                                            <div className="icon">
                                                <i className={contactInfo.phone.icon}></i>
                                            </div>
                                        </div>
                                        {/* Contenu textuel du téléphone */}
                                        <div className="office-info-text">
                                            <h2>{contactInfo.phone.title}</h2>
                                            {/* Affichage du numéro de téléphone DEUTZA */}
                                            <p>
                                                <a href={`tel:${contactInfo.phone.content[0]}`}>
                                                    {contactInfo.phone.content[0]}
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

/*
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
                                            EXPORT DU COMPOSANT
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
*/

/**
 * Export par défaut du composant Contactpage
 *
 * @exports Contactpage
 * @description Composant React pour la page de contact de DEUTZA
 *
 * @usage
 * // Import dans un autre composant
 * import Contactpage from './components/Contactpage/Contactpage';
 *
 * // Utilisation dans une route React Router
 * <Route path="/contact" element={<Contactpage />} />
 *
 * @features
 * ✅ Traduction complète en français
 * ✅ Informations de contact DEUTZA mises à jour
 * ✅ Design responsive (mobile-first)
 * ✅ Liens cliquables (mailto: et tel:)
 * ✅ Icônes Font Awesome intégrées
 * ✅ Documentation complète JSDoc
 * ✅ Code commenté et structuré
 *
 * @contact
 * 📧 Email: <EMAIL>
 * 📞 Téléphone: 20440595
 * 📍 Adresse: Nabeul, Tunisie
 */
export default Contactpage;

/*
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                            FIN DU FICHIER                                                      │
│                                                                                                                 │
│  📝 RÉSUMÉ DES MODIFICATIONS:                                                                                   │
│     • Traduction complète en français                                                                          │
│     • Mise à jour des informations de contact DEUTZA                                                           │
│     • Ajout de liens cliquables (mailto: et tel:)                                                              │
│     • Documentation JSDoc complète                                                                             │
│     • Commentaires détaillés pour chaque section                                                               │
│     • Structure de données configurables                                                                       │
│     • Code optimisé et maintenable                                                                             │
│                                                                                                                 │
│  🚀 PRÊT POUR LA PRODUCTION                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
*/
