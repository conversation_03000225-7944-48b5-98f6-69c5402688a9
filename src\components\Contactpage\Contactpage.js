/*
╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║                                        PAGE DE CONTACT - DEUTZA                                                 ║
╠══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╣
║                                                                                                                  ║
║  📋 DESCRIPTION:                                                                                                 ║
║      Composant React pour la page de contact de la plateforme DEUTZA                                            ║
║      Affiche les informations de contact principales : adresse, email et téléphone                              ║
║                                                                                                                  ║
║  📍 INFORMATIONS DE CONTACT:                                                                                     ║
║      • Adresse: <PERSON><PERSON><PERSON>, Tunisie                                                                                 ║
║      • Email principal: <EMAIL>                                                                      ║
║      • Téléphone: 20440595                                                                                      ║
║                                                                                                                  ║
║  🎨 STRUCTURE:                                                                                                   ║
║      • Section responsive avec 3 colonnes d'informations                                                        ║
║      • Icônes Font Awesome pour chaque type de contact                                                          ║
║      • Design adaptatif pour mobile, tablette et desktop                                                        ║
║                                                                                                                  ║
║  🔧 TECHNOLOGIES:                                                                                                ║
║      • React 18+ (Hooks: useState)                                                                              ║
║      • React Router (useNavigate pour navigation)                                                               ║
║      • Bootstrap Grid System (responsive)                                                                       ║
║      • Font Awesome Icons (flaticon)                                                                            ║
║                                                                                                                  ║
║  📱 RESPONSIVE DESIGN:                                                                                           ║
║      • XL (1200px+): 3 colonnes côte à côte                                                                     ║
║      • LG (992px+): 2 colonnes par ligne                                                                        ║
║      • MD (768px+): 2 colonnes par ligne                                                                        ║
║      • SM (576px-): 1 colonne par ligne                                                                         ║
║                                                                                                                  ║
║  👤 AUTEUR: Équipe Développement DEUTZA                                                                         ║
║  📅 DATE: 2025                                                                                                  ║
║  📝 VERSION: 2.0 - Traduction française et mise à jour des contacts                                             ║
║                                                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
*/

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *                                        COMPOSANT PAGE DE CONTACT
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * @component Contactpage
 * @description Composant principal pour afficher les informations de contact de DEUTZA
 * @returns {JSX.Element} Interface de contact avec adresse, email et téléphone
 *
 * @example
 * // Utilisation dans une route
 * <Route path="/contact" element={<Contactpage />} />
 *
 * @features
 * - Affichage responsive des informations de contact
 * - Icônes visuelles pour chaque type de contact
 * - Design adaptatif pour tous les écrans
 * - Informations de contact mises à jour pour DEUTZA
 */
const Contactpage = () => {
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            HOOKS ET ÉTAT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Hook de navigation React Router pour redirection programmatique
     * @type {Function}
     */
    const navigate = useNavigate();

    /**
     * État local pour gérer les interactions futures (extensibilité)
     * @type {[any, Function]}
     */
    const [contactState] = useState({
        // Réservé pour futures fonctionnalités (formulaire de contact, etc.)
    });

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            DONNÉES DE CONTACT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Configuration des informations de contact DEUTZA
     * @constant {Object} contactInfo
     */
    const contactInfo = {
        address: {
            title: "Adresse",
            content: ["Nabeul", "Tunisie"],
            icon: "fi flaticon-maps-and-flags"
        },
        email: {
            title: "Nous contacter par email",
            content: ["<EMAIL>"],
            icon: "fi flaticon-email"
        },
        phone: {
            title: "Appelez-nous",
            content: ["20440595"],
            icon: "fi flaticon-phone-call"
        }
    };

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            STYLES POUR UNIFORMISER LES CARTES
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    /**
     * Styles CSS pour uniformiser la taille des cartes de contact
     * @constant {Object} cardStyles
     */
    const cardStyles = {
        // Style pour chaque carte de contact
        contactCard: {
            height: '100%',                    // Hauteur complète du conteneur parent
            minHeight: '280px',                // Hauteur minimale pour uniformité
            display: 'flex',                   // Flexbox pour alignement
            flexDirection: 'column',           // Direction verticale
            justifyContent: 'center',          // Centrage vertical du contenu
            padding: '30px 20px',              // Espacement interne uniforme
            border: '1px solid #e5e7eb',       // Bordure subtile
            borderRadius: '12px',              // Bordures arrondies modernes
            backgroundColor: '#ffffff',        // Fond blanc
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)', // Ombre douce
            transition: 'all 0.3s ease',       // Transition fluide pour les interactions
            position: 'relative',              // Position relative pour les effets
            overflow: 'hidden'                 // Masquer les débordements
        },

        // Style pour l'effet de survol
        contactCardHover: {
            transform: 'translateY(-5px)',     // Légère élévation au survol
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)' // Ombre plus prononcée
        },

        // Style pour l'icône
        iconContainer: {
            marginBottom: '20px',              // Espacement sous l'icône
            textAlign: 'center'                // Centrage de l'icône
        },

        // Style pour le contenu textuel
        textContainer: {
            textAlign: 'center',               // Centrage du texte
            flex: '1',                         // Prendre l'espace disponible
            display: 'flex',                   // Flexbox pour le contenu
            flexDirection: 'column',           // Direction verticale
            justifyContent: 'center'           // Centrage vertical
        },

        // Style pour les titres
        cardTitle: {
            fontSize: '1.25rem',               // Taille de police uniforme
            fontWeight: '600',                 // Poids de police semi-gras
            marginBottom: '15px',              // Espacement sous le titre
            color: '#1f2937',                  // Couleur de texte foncée
            lineHeight: '1.4'                  // Hauteur de ligne optimale
        },

        // Style pour le contenu
        cardContent: {
            fontSize: '1rem',                  // Taille de police standard
            color: '#6b7280',                  // Couleur de texte grise
            lineHeight: '1.6',                // Hauteur de ligne pour lisibilité
            margin: '0'                        // Suppression des marges par défaut
        },

        // Style pour les liens
        cardLink: {
            color: '#3b82f6',                  // Couleur bleue pour les liens
            textDecoration: 'none',            // Suppression du soulignement
            transition: 'color 0.3s ease'     // Transition pour le changement de couleur
        }
    };

    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════
    //                                            RENDU DU COMPOSANT
    // ═══════════════════════════════════════════════════════════════════════════════════════════════════════════

    return (
        /*
        ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │                                    SECTION PRINCIPALE DE CONTACT                                           │
        └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        */
        <section className="wpo-contact-pg-section section-padding">
            {/* Conteneur Bootstrap pour la mise en page responsive */}
            <div className="container">
                <div className="row">
                    {/* Colonne centrée avec offset pour un design équilibré */}
                    <div className="col col-lg-10 offset-lg-1">
                        {/* Conteneur principal des informations de contact */}
                        <div className="office-info">
                            <div className="row">

                                {/*
                                ┌─────────────────────────────────────────────────────────────────────────────────┐
                                │                                SECTION ADRESSE                                 │
                                └─────────────────────────────────────────────────────────────────────────────────┘
                                */}
                                <div className="col col-xl-4 col-lg-6 col-md-6 col-12">
                                    <div
                                        className="office-info-item"
                                        style={cardStyles.contactCard}
                                        onMouseEnter={(e) => {
                                            Object.assign(e.target.style, cardStyles.contactCardHover);
                                        }}
                                        onMouseLeave={(e) => {
                                            Object.assign(e.target.style, cardStyles.contactCard);
                                        }}
                                    >
                                        {/* Icône d'adresse avec style Font Awesome */}
                                        <div className="office-info-icon" style={cardStyles.iconContainer}>
                                            <div className="icon">
                                                <i className={contactInfo.address.icon}></i>
                                            </div>
                                        </div>
                                        {/* Contenu textuel de l'adresse */}
                                        <div className="office-info-text" style={cardStyles.textContainer}>
                                            <h2 style={cardStyles.cardTitle}>{contactInfo.address.title}</h2>
                                            <p style={cardStyles.cardContent}>
                                                {contactInfo.address.content[0]} <br/>
                                                {contactInfo.address.content[1]}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/*
                                ┌─────────────────────────────────────────────────────────────────────────────────┐
                                │                                SECTION EMAIL                                   │
                                └─────────────────────────────────────────────────────────────────────────────────┘
                                */}
                                <div className="col col-xl-4 col-lg-6 col-md-6 col-12">
                                    <div
                                        className="office-info-item"
                                        style={cardStyles.contactCard}
                                        onMouseEnter={(e) => {
                                            Object.assign(e.target.style, cardStyles.contactCardHover);
                                        }}
                                        onMouseLeave={(e) => {
                                            Object.assign(e.target.style, cardStyles.contactCard);
                                        }}
                                    >
                                        {/* Icône d'email avec style Font Awesome */}
                                        <div className="office-info-icon" style={cardStyles.iconContainer}>
                                            <div className="icon">
                                                <i className={contactInfo.email.icon}></i>
                                            </div>
                                        </div>
                                        {/* Contenu textuel de l'email */}
                                        <div className="office-info-text" style={cardStyles.textContainer}>
                                            <h2 style={cardStyles.cardTitle}>{contactInfo.email.title}</h2>
                                            {/* Affichage de l'email principal DEUTZA */}
                                            <p style={cardStyles.cardContent}>
                                                <a
                                                    href={`mailto:${contactInfo.email.content[0]}`}
                                                    style={cardStyles.cardLink}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.color = '#1d4ed8';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.color = '#3b82f6';
                                                    }}
                                                >
                                                    {contactInfo.email.content[0]}
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/*
                                ┌─────────────────────────────────────────────────────────────────────────────────┐
                                │                               SECTION TÉLÉPHONE                               │
                                └─────────────────────────────────────────────────────────────────────────────────┘
                                */}
                                <div className="col col-xl-4 col-lg-6 col-md-6 col-12">
                                    <div
                                        className="office-info-item"
                                        style={cardStyles.contactCard}
                                        onMouseEnter={(e) => {
                                            Object.assign(e.target.style, cardStyles.contactCardHover);
                                        }}
                                        onMouseLeave={(e) => {
                                            Object.assign(e.target.style, cardStyles.contactCard);
                                        }}
                                    >
                                        {/* Icône de téléphone avec style Font Awesome */}
                                        <div className="office-info-icon" style={cardStyles.iconContainer}>
                                            <div className="icon">
                                                <i className={contactInfo.phone.icon}></i>
                                            </div>
                                        </div>
                                        {/* Contenu textuel du téléphone */}
                                        <div className="office-info-text" style={cardStyles.textContainer}>
                                            <h2 style={cardStyles.cardTitle}>{contactInfo.phone.title}</h2>
                                            {/* Affichage du numéro de téléphone DEUTZA */}
                                            <p style={cardStyles.cardContent}>
                                                <a
                                                    href={`tel:${contactInfo.phone.content[0]}`}
                                                    style={cardStyles.cardLink}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.color = '#1d4ed8';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.color = '#3b82f6';
                                                    }}
                                                >
                                                    {contactInfo.phone.content[0]}
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

/*
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
                                            EXPORT DU COMPOSANT
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
*/

/**
 * Export par défaut du composant Contactpage
 *
 * @exports Contactpage
 * @description Composant React pour la page de contact de DEUTZA
 *
 * @usage
 * // Import dans un autre composant
 * import Contactpage from './components/Contactpage/Contactpage';
 *
 * // Utilisation dans une route React Router
 * <Route path="/contact" element={<Contactpage />} />
 *
 * @features
 * ✅ Traduction complète en français
 * ✅ Informations de contact DEUTZA mises à jour
 * ✅ Design responsive (mobile-first)
 * ✅ Liens cliquables (mailto: et tel:)
 * ✅ Icônes Font Awesome intégrées
 * ✅ Documentation complète JSDoc
 * ✅ Code commenté et structuré
 *
 * @contact
 * 📧 Email: <EMAIL>
 * 📞 Téléphone: 20440595
 * 📍 Adresse: Nabeul, Tunisie
 */
export default Contactpage;

/*
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                            FIN DU FICHIER                                                      │
│                                                                                                                 │
│  📝 RÉSUMÉ DES MODIFICATIONS:                                                                                   │
│     • Traduction complète en français                                                                          │
│     • Mise à jour des informations de contact DEUTZA                                                           │
│     • Ajout de liens cliquables (mailto: et tel:)                                                              │
│     • Documentation JSDoc complète                                                                             │
│     • Commentaires détaillés pour chaque section                                                               │
│     • Structure de données configurables                                                                       │
│     • Code optimisé et maintenable                                                                             │
│                                                                                                                 │
│  🚀 PRÊT POUR LA PRODUCTION                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
*/
