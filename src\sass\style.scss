/*------------------------------------------------------------------
[Master Stylesheet]
Theme Name:     Eduko - Online Course & Education HTML5 Template
Version:        1.0.0
-------------------------------------------------------------------*/


/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------

1. General
	1.1 Theme Reset Style
	1.2 Global Elements

2. header
	2.1 topbar
	2.2 navigation

3. content
	3.1 wpo-hero-slider
	3.2 wpo-about-section
	3.3 wpo-courses-section
	3.4 wpo-popular-area
	3.5 wpo-testimonial-section
	3.6 wpo-team-section
	3.7 wpo-choose-section
	3.8 wpo-blog-section
	3.9 wpo-subscribe-section

4. wpo-footer

5. Home-style-2
   5.1 wpo-features-area
   5.2 wpo-about-section-s2
   5.3 wpo-courses-section-s2
   5.4 wpo-choose-section-s2
   5.6 wpo-subscribe-section-s2

6. Home-style-3
   6.1 wpo-features-area-s2
   6.2 wpo-about-section-s3
   6.3 wpo-courses-section-s3
   6.4 wpo-popular-area-s2
   6.5 wpo-event-area

7. Home-style-4
   7.1 wpo-about-section-s4
   7.2 wpo-fun-fact-section
   7.3 wpo-courses-section-s3
   7.4 wpo-popular-area-s3
   7.5 wpo-testimonial-section-s2
   7.6 wpo-blog-section-s2

8. Home-style-5
   8.1 wpo-features-area-s3
   8.2 wpo-about-section-s5
   8.3 wpo-fun-fact-section-s2
   8.4 wpo-courses-section-s5
   8.5 wpo-popular-area-s4

9. wpo-course-page

10. course-details-page

11. wpo-lesson-section

12. teacher-page

13. wpo-team-single

14. wpo-gallery-page

15. wpo-shop-page

16. wpo-shop-single-page

17. wpo-cart-page-style

18. wpo-checkout-page-style

19. wpo-faq-page

20. wpo-blog-pg-section

21. wpo-blog-single-section

22. wpo-contact-pg-section

23. error-404-section

24. wpo-terms-section

----------------------------------------------------------------*/



/*------------------------------------------------------------------
1. General
----------------------------------------------------------------*/

// helpers
@import "helpers/variables";
@import "helpers/mixins";

// base
@import "base/base";


// components
@import "components/section-title";
@import "components/buttons";
@import "components/form";
@import "components/social-links";
@import "components/page-title";
@import "components/blog-sidebar";
@import "components/pagination";

// layout
@import "layout/header";
@import "layout/hero-slider";
@import "layout/footer";

// components
@import "components/sticky-header";


// page
@import "page/home-default";

@import "page/home-style-2";

@import "page/home-style-3";

@import "page/home-style-4";

@import "page/home-style-5";

@import "page/course";

@import "page/course-single";

@import "page/lesson";

@import "page/teacher";

@import "page/team-single";

@import "page/gallery";

@import "page/shop";

@import "page/shop-single";

@import "page/service-single";

@import "page/checkout";

@import "page/cart";

@import "page/faq";

@import "page/_blog";

@import "page/_blog-single";

@import "page/contact";

@import "page/error-404";

@import "page/terms";




