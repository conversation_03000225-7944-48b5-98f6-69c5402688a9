import React from 'react'
import { Link } from 'react-router-dom'
import Logo from '../../images/deutzaaa_blanc__1_-removebg-preview.png'

const ClickHandler = () => {
    window.scrollTo(10, 0);
}

const Footer = (props) => {
    return (
        <footer className="wpo-site-footer">
            <div className="wpo-upper-footer">
                <div className="container">
                    <div className="row">
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget about-widget">
                                <div className="logo widget-title">
                                    <Link onClick={ClickHandler} className="navbar-brand" to="/home"><img src={Logo}
                                            alt=""/></Link>
                                </div>
                                <p>Notre site E-learning propose des cours en ligne accessibles 24h/24. Les utilisateurs peuvent apprendre à leur rythme grâce à des vidéos, des exercices et des sessions en direct. C’est une solution flexible et moderne pour se former où que l’on soit.</p>
                                <div className="social">
                                <ul>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-facebook"></i>
                                        </Link>
                                    </li>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-twitter-alt"></i>
                                        </Link>
                                    </li>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-instagram"></i>
                                        </Link>
                                    </li>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-google"></i>
                                        </Link>
                                    </li>
                                </ul>
                                </div>
                            </div>
                        </div>
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget link-widget">
                                <div className="widget-title">
                                    <h3>Liens rapides</h3>
                                </div>
                                <ul>
                                    <li><Link onClick={ClickHandler} to="/home">Accueil</Link></li>
                                    <li><Link onClick={ClickHandler} to="/about">À propos de nous</Link></li>
                                    <li><Link onClick={ClickHandler} to="/course">Nos Matières</Link></li>
                                    <li><Link onClick={ClickHandler} to="/teacher">Enseignants</Link></li>
                                    <li><Link onClick={ClickHandler} to="/blog">Dernières actualités</Link></li>
                                </ul>
                            </div>
                        </div>
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget link-widget s2">
                                <div className="widget-title">
                                    <h3> Liens utiles</h3>
                                </div>
                                <ul>
                                    <li><Link onClick={ClickHandler} to="/contact">Nous Contact </Link></li>
                                    <li><Link onClick={ClickHandler} to="/course">Matières</Link></li>
                                    <li><Link onClick={ClickHandler} to="/lesson">Abonnement</Link></li>
                                    <li><Link onClick={ClickHandler} to="/register">S'inscrire</Link></li>
                                    <li><Link onClick={ClickHandler} to="/testimonial">Témoignages</Link></li>
                                </ul>
                            </div>
                        </div>
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget wpo-contact-widget">
                                <div className="widget-title">
                                    <h3>Nous Contact</h3>
                                </div>
                                <div className="contact-ft">
                                    <ul>
                                        <li><i className="fi flaticon-email"></i><EMAIL></li>
                                        <li><i className="fi flaticon-phone-call"></i>20440595 
                                        {/*<br/> (************* */}</li>
                                        <li><i className="fi flaticon-placeholder"></i>Nabeul. <br/> Tunisie</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
     {/** <div className="wpo-lower-footer">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col col-lg-6 col-md-12 col-12">
                            <ul>
                                <li>&copy; 2023 <Link onClick={ClickHandler} to="/">Eduko</Link>. All rights reserved.</li>
                            </ul>
                        </div>
                        <div className="col col-lg-6 col-md-12 col-12">
                            <div className="link">
                                <ul>
                                    <li><Link onClick={ClickHandler} to="/privacy">Privace & Policy</Link></li>
                                    <li><Link onClick={ClickHandler} to="/terms">Terms</Link></li>
                                    <li><Link onClick={ClickHandler} to="/about">About us</Link></li>
                                    <li><Link onClick={ClickHandler} to="/faq">FAQ</Link></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>*/}       
        </footer>
    )
}

export default Footer;