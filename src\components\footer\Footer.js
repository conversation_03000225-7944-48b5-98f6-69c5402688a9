import React from 'react'
import { Link } from 'react-router-dom'
import Logo from '../../images/deutzaaa_blanc__1_-removebg-preview.png'

const ClickHandler = () => {
    window.scrollTo(10, 0);
}

const Footer = (props) => {
    return (
        <footer className="wpo-site-footer">
            <div className="wpo-upper-footer">
                <div className="container">
                    <div className="row">
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget about-widget">
                                <div className="logo widget-title">
                                    <Link onClick={ClickHandler} className="navbar-brand" to="/home"><img src={Logo}
                                            alt=""/></Link>
                                </div>
                                <p>Mattis inelit neque quis donec eleifnd amet. Amet sed et cursus eu euismod. Egestas in morbi tristique ornare vulputate vitae enim.</p>
                                <div className="social">
                                <ul>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-facebook"></i>
                                        </Link>
                                    </li>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-twitter-alt"></i>
                                        </Link>
                                    </li>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-instagram"></i>
                                        </Link>
                                    </li>
                                    <li>
                                        <Link onClick={ClickHandler} to="/">
                                            <i className="ti-google"></i>
                                        </Link>
                                    </li>
                                </ul>
                                </div>
                            </div>
                        </div>
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget link-widget">
                                <div className="widget-title">
                                    <h3>Liens rapides</h3>
                                </div>
                                <ul>
                                    <li><Link onClick={ClickHandler} to="/home">Accueil</Link></li>
                                    <li><Link onClick={ClickHandler} to="/about">À propos de nous</Link></li>
                                    <li><Link onClick={ClickHandler} to="/course">Cours en vedette</Link></li>
                                    <li><Link onClick={ClickHandler} to="/teacher">Enseignants</Link></li>
                                    <li><Link onClick={ClickHandler} to="/blog">Dernières actualités</Link></li>
                                </ul>
                            </div>
                        </div>
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget link-widget s2">
                                <div className="widget-title">
                                    <h3> Liens utiles</h3>
                                </div>
                                <ul>
                                    <li><Link onClick={ClickHandler} to="/contact">Nous Contact </Link></li>
                                    <li><Link onClick={ClickHandler} to="/course">Cours</Link></li>
                                    <li><Link onClick={ClickHandler} to="/lesson">Abonnement</Link></li>
                                    <li><Link onClick={ClickHandler} to="/register">S'inscrire</Link></li>
                                    <li><Link onClick={ClickHandler} to="/testimonial">Témoignages</Link></li>
                                </ul>
                            </div>
                        </div>
                        <div className="col col-lg-3 col-md-6 col-12 col-md-6 col-sm-12 col-12">
                            <div className="widget wpo-contact-widget">
                                <div className="widget-title">
                                    <h3>Nous Contact</h3>
                                </div>
                                <div className="contact-ft">
                                    <ul>
                                        <li><i className="fi flaticon-email"></i><EMAIL></li>
                                        <li><i className="fi flaticon-phone-call"></i>(************* <br/>
                                            (*************</li>
                                        <li><i className="fi flaticon-placeholder"></i>4517 Washington Ave. <br/> Manchter, Kentucky 495</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
     {/** <div className="wpo-lower-footer">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col col-lg-6 col-md-12 col-12">
                            <ul>
                                <li>&copy; 2023 <Link onClick={ClickHandler} to="/">Eduko</Link>. All rights reserved.</li>
                            </ul>
                        </div>
                        <div className="col col-lg-6 col-md-12 col-12">
                            <div className="link">
                                <ul>
                                    <li><Link onClick={ClickHandler} to="/privacy">Privace & Policy</Link></li>
                                    <li><Link onClick={ClickHandler} to="/terms">Terms</Link></li>
                                    <li><Link onClick={ClickHandler} to="/about">About us</Link></li>
                                    <li><Link onClick={ClickHandler} to="/faq">FAQ</Link></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>*/}       
        </footer>
    )
}

export default Footer;